{"name": "kraftapp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/app-check": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-firebase/messaging": "^22.2.0", "@react-navigation/bottom-tabs": "^7.3.12", "@react-navigation/drawer": "^7.3.12", "@react-navigation/native": "^7.1.8", "@react-navigation/stack": "^7.3.1", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/runtime": "^7.25.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/react": "^19.0.0", "babel-preset-expo": "^13.1.11", "eslint": "^8.19.0"}, "engines": {"node": ">=18"}}
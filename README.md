# Kraft Paper Application Backend

Backend API for the Kraft Paper Application, a platform for managing kraft paper inventory, orders, and customer interactions.

## Features

- User authentication and authorization
- Stock inventory management
- Shopping cart functionality
- Order processing and management
- Customer enquiry system
- Notifications
- Admin dashboard and analytics
- Tally integration for financial management

## Tech Stack

- **Node.js** - JavaScript runtime
- **Express.js** - Web framework
- **TypeScript** - Type-safe JavaScript
- **PostgreSQL** - Relational database
- **Prisma** - ORM for database access
- **Redis** - Caching and session management
- **JWT** - Authentication
- **Joi** - Input validation
- **Firebase Admin** - Phone OTP authentication
- **Docker** - Containerization
- **Swagger** - API documentation

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Docker and Docker Compose (for containerized setup)
- PostgreSQL (if running locally)
- Redis (if running locally)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/harshit3478/kraft-backend.git
   cd kraft-backend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

4. Generate Prisma client:
   ```bash
   npm run prisma:generate
   ```

5. Run database migrations:
   ```bash
   npm run prisma:migrate
   ```

### Running the Application

#### Using Docker (recommended)

```bash
# Start all services
npm run docker:up

# Stop all services
npm run docker:down
```

#### Local Development

```bash
# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### API Documentation

API documentation is available at `/api-docs` when the server is running.

## Project Structure

```
kraft-backend/
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Request handlers
│   ├── middlewares/    # Express middlewares
│   ├── models/         # Data models
│   ├── routes/         # API routes
│   ├── services/       # Business logic
│   ├── utils/          # Utility functions
│   └── validations/    # Input validation schemas
├── prisma/             # Prisma schema and migrations
├── .env.example        # Example environment variables
├── .eslintrc.json      # ESLint configuration
├── .prettierrc         # Prettier configuration
├── docker-compose.yml  # Docker Compose configuration
├── Dockerfile          # Docker configuration
├── package.json        # Project dependencies
└── tsconfig.json       # TypeScript configuration
```

## License

This project is licensed under the ISC License.

## Author

Harshit Agarwal

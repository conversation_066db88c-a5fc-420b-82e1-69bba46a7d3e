import { Router } from 'express';
import { authenticate, authorize } from '../middlewares/auth.middleware';
import validate from '../middlewares/validate.middleware';
import { createStockSchema, updateStockSchema, stockQuerySchema } from '../validations/stock.validation';
import {
  getAllStocks,
  getStock,
  createStockItem,
  updateStockItem,
  deleteStockItem,
  importStockItems,
  exportStockToCSV
} from '../controllers/stock.controller';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads');
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'stock-import-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  fileFilter: function (req, file, cb) {
    console.log('File upload request received:', file);
    // Accept only CSV files
    if (file.mimetype !== 'text/csv' && !file.originalname.endsWith('.csv')) {
      console.log('File rejected - not a CSV:', file.mimetype, file.originalname);
      return cb(new Error('Only CSV files are allowed'), false);
    }
    console.log('File accepted:', file.originalname);
    cb(null, true);
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

/**
 * @swagger
 * /api/stock:
 *   get:
 *     summary: Get all stock items
 *     tags: [Stock]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by paper type
 *       - in: query
 *         name: gsm
 *         schema:
 *           type: integer
 *         description: Filter by GSM
 *       - in: query
 *         name: bf
 *         schema:
 *           type: integer
 *         description: Filter by BF (Bursting Factor)
 *     responses:
 *       200:
 *         description: Stock items retrieved successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/', authenticate, validate(stockQuerySchema, 'query'), getAllStocks);

/**
 * @swagger
 * /api/stock/export:
 *   get:
 *     summary: Export all stock items to CSV
 *     tags: [Stock]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Stock data exported successfully
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/export', authenticate, exportStockToCSV);

/**
 * @swagger
 * /api/stock/{id}:
 *   get:
 *     summary: Get stock item by ID
 *     tags: [Stock]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Stock ID
 *     responses:
 *       200:
 *         description: Stock item retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Stock item not found
 */
router.get('/:id', authenticate, getStock);

/**
 * @swagger
 * /api/stock:
 *   post:
 *     summary: Create new stock item
 *     tags: [Stock]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - gsm
 *               - bf
 *               - rollsAvailable
 *               - pricePerRoll
 *             properties:
 *               type:
 *                 type: string
 *               gsm:
 *                 type: integer
 *               bf:
 *                 type: integer
 *               rollsAvailable:
 *                 type: integer
 *               pricePerRoll:
 *                 type: number
 *     responses:
 *       201:
 *         description: Stock item created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin only
 */
router.post('/', authenticate, authorize(['ADMIN']), validate(createStockSchema), createStockItem);

/**
 * @swagger
 * /api/stock/import:
 *   post:
 *     summary: Import stock items from CSV file
 *     tags: [Stock]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: file
 *         type: file
 *         required: true
 *         description: CSV file containing stock data
 *     responses:
 *       200:
 *         description: Stock items imported successfully
 *       400:
 *         description: Invalid file or data format
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin only
 */
// Add error handling middleware for multer
const handleMulterError = (err: any, req: any, res: any, next: any) => {
  console.error('Multer error:', err);
  if (err instanceof multer.MulterError) {
    return res.status(400).json({
      status: 'error',
      message: `Upload error: ${err.message}`
    });
  } else if (err) {
    return res.status(400).json({
      status: 'error',
      message: err.message || 'Unknown error during file upload'
    });
  }
  next();
};

router.post('/import', authenticate, authorize(['ADMIN']), (req, res, next) => {
  console.log('Import endpoint hit, processing upload');
  upload.single('file')(req, res, (err) => {
    if (err) {
      console.error('Upload error:', err);
      return res.status(400).json({
        status: 'error',
        message: err.message || 'Error uploading file'
      });
    }
    next();
  });
}, importStockItems);

/**
 * @swagger
 * /api/stock/{id}:
 *   put:
 *     summary: Update stock item
 *     tags: [Stock]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Stock ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               rollsAvailable:
 *                 type: integer
 *               pricePerRoll:
 *                 type: number
 *     responses:
 *       200:
 *         description: Stock item updated successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin only
 *       404:
 *         description: Stock item not found
 */
router.put('/:id', authenticate, authorize(['ADMIN']), validate(updateStockSchema), updateStockItem);

/**
 * @swagger
 * /api/stock/{id}:
 *   delete:
 *     summary: Delete stock item
 *     tags: [Stock]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Stock ID
 *     responses:
 *       200:
 *         description: Stock item deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Admin only
 *       404:
 *         description: Stock item not found
 */
router.delete('/:id', authenticate, authorize(['ADMIN']), deleteStockItem);

export default router;

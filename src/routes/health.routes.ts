import { Router } from 'express';
import prisma from '../config/database';
import redisClient from '../config/redis';

const router = Router();

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Check system health
 *     tags: [Health]
 *     responses:
 *       200:
 *         description: System is healthy
 *       500:
 *         description: System is unhealthy
 */
router.get('/', async (req, res) => {
  const health = {
    uptime: process.uptime(),
    timestamp: Date.now(),
    services: {
      database: {
        status: 'unknown',
        message: ''
      },
      redis: {
        status: 'unknown',
        message: ''
      }
    }
  };

  // Check database connection
  try {
    await prisma.$queryRaw`SELECT 1`;
    health.services.database.status = 'healthy';
  } catch (error: any) {
    health.services.database.status = 'unhealthy';
    health.services.database.message = error.message;
  }

  // Check Redis connection
  try {
    if (redisClient.isOpen) {
      await redisClient.ping();
      health.services.redis.status = 'healthy';
    } else {
      health.services.redis.status = 'disconnected';
      health.services.redis.message = 'Redis client is not connected';
    }
  } catch (error: any) {
    health.services.redis.status = 'unhealthy';
    health.services.redis.message = error.message;
  }

  // Determine overall health
  const isHealthy = 
    health.services.database.status === 'healthy' && 
    (health.services.redis.status === 'healthy' || health.services.redis.status === 'disconnected');

  return res.status(isHealthy ? 200 : 500).json(health);
});

export default router;

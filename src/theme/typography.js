/**
 * Typography styles for the Kraft Paper app
 */

import { Platform } from 'react-native';

// Font family
const fontFamily = Platform.select({
  ios: {
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
  android: {
    regular: 'Roboto',
    medium: 'Roboto-Medium',
    bold: 'Roboto-Bold',
  },
  default: {  // Fallback for any other platform
    regular: 'System',
    medium: 'System',
    bold: 'System',
  },
});

// Font sizes
const fontSize = {
  tiny: 10,
  small: 12,
  regular: 14,
  medium: 16,
  large: 18,
  xlarge: 20,
  xxlarge: 24,
  xxxlarge: 30,
};

// Line heights
const lineHeight = {
  tiny: 14,
  small: 18,
  regular: 22,
  medium: 24,
  large: 26,
  xlarge: 28,
  xxlarge: 32,
  xxxlarge: 38,
};

// Font weights
const fontWeight = {
  thin: '100',
  light: '300',
  regular: '400',
  medium: '500',
  bold: '700',
  black: '900',
};

// Get font family safely
const getFontFamily = (type) => {
  // Make sure fontFamily and fontFamily[Platform.OS] exist before accessing properties
  if (fontFamily && fontFamily[Platform.OS] && fontFamily[Platform.OS][type]) {
    return fontFamily[Platform.OS][type];
  }
  // Fallback to default font
  return Platform.OS === 'ios' ? 'System' : 'Roboto';
};

// Text styles
const textStyles = {
  heading1: {
    fontFamily: getFontFamily('bold'),
    fontSize: fontSize.xxxlarge,
    lineHeight: lineHeight.xxxlarge,
    fontWeight: fontWeight.bold,
  },
  heading2: {
    fontFamily: getFontFamily('bold'),
    fontSize: fontSize.xxlarge,
    lineHeight: lineHeight.xxlarge,
    fontWeight: fontWeight.bold,
  },
  heading3: {
    fontFamily: getFontFamily('bold'),
    fontSize: fontSize.xlarge,
    lineHeight: lineHeight.xlarge,
    fontWeight: fontWeight.bold,
  },
  heading4: {
    fontFamily: getFontFamily('medium'),
    fontSize: fontSize.large,
    lineHeight: lineHeight.large,
    fontWeight: fontWeight.medium,
  },
  body1: {
    fontFamily: getFontFamily('regular'),
    fontSize: fontSize.medium,
    lineHeight: lineHeight.medium,
    fontWeight: fontWeight.regular,
  },
  body2: {
    fontFamily: getFontFamily('regular'),
    fontSize: fontSize.regular,
    lineHeight: lineHeight.regular,
    fontWeight: fontWeight.regular,
  },
  caption: {
    fontFamily: getFontFamily('regular'),
    fontSize: fontSize.small,
    lineHeight: lineHeight.small,
    fontWeight: fontWeight.regular,
  },
  button: {
    fontFamily: getFontFamily('medium'),
    fontSize: fontSize.medium,
    lineHeight: lineHeight.medium,
    fontWeight: fontWeight.medium,
  },
};

export { fontFamily, fontSize, lineHeight, fontWeight, textStyles };
export default textStyles;

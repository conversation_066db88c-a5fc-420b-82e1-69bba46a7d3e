/**
 * Utility functions for handling order statuses
 */

// Valid order statuses from the backend
export const ORDER_STATUS = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
};

/**
 * Maps UI status filters to backend status values
 * @param {string} uiStatus - UI status filter ('active' or 'completed')
 * @returns {string|undefined} - Backend status value or undefined to fetch all
 */
export const mapUiStatusToApiStatus = (uiStatus) => {
  // We don't map directly since we want to filter client-side
  // to show multiple statuses in each tab
  return undefined; // Return undefined to fetch all statuses
};

/**
 * Checks if an order status belongs to "active" orders
 * @param {string} status - Order status
 * @returns {boolean} - Whether the status is considered active
 */
export const isActiveOrderStatus = (status) => {
  if (!status) return false;
  const upperStatus = status.toUpperCase();
  return [
    ORDER_STATUS.PENDING,
    ORDER_STATUS.APPROVED,
    ORDER_STATUS.SHIPPED,
  ].includes(upperStatus);
};

/**
 * Checks if an order status belongs to "completed" orders
 * @param {string} status - Order status
 * @returns {boolean} - Whether the status is considered completed
 */
export const isCompletedOrderStatus = (status) => {
  if (!status) return false;
  const upperStatus = status.toUpperCase();
  return [
    ORDER_STATUS.DELIVERED,
    ORDER_STATUS.CANCELLED,
  ].includes(upperStatus);
};

/**
 * Gets a user-friendly display name for an order status
 * @param {string} status - Order status
 * @returns {string} - Display name
 */
export const getOrderStatusDisplayName = (status) => {
  if (!status) return 'Unknown';
  
  const displayNames = {
    [ORDER_STATUS.PENDING]: 'Pending',
    [ORDER_STATUS.APPROVED]: 'Approved',
    [ORDER_STATUS.SHIPPED]: 'Shipped',
    [ORDER_STATUS.DELIVERED]: 'Delivered',
    [ORDER_STATUS.CANCELLED]: 'Cancelled',
  };
  
  return displayNames[status.toUpperCase()] || status;
};

import { useEffect, useState } from 'react';
import messaging from '@react-native-firebase/messaging';
import auth from '@react-native-firebase/auth';
import { Platform } from 'react-native';
import { useFirebaseRecaptcha } from './recaptcha';
import { API_BASE_URL } from '../config';

/**
 * Initialize Firebase services
 */
export const initializeFirebase = async () => {
  try {
    // Request permission for notifications (required for iOS)
    if (Platform.OS === 'ios') {
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      
      if (enabled) {
        console.log('Firebase Messaging permission granted');
      } else {
        console.log('Firebase Messaging permission denied');
      }
    }
    
    // Get FCM token
    const token = await messaging().getToken();
    console.log('FCM Token:', token);
    
    return token;
  } catch (error) {
    console.error('Firebase initialization error:', error);
    return null;
  }
};

/**
 * Hook to handle Firebase messaging
 */
export const useFirebaseMessaging = () => {
  const [notification, setNotification] = useState(null);
  
  useEffect(() => {
    // Handle notifications when the app is in the foreground
    const unsubscribe = messaging().onMessage(async remoteMessage => {
      console.log('Foreground Message received:', remoteMessage);
      setNotification(remoteMessage);
    });
    
    // Handle notification when the app is in the background and opened
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('Background Message opened:', remoteMessage);
      setNotification(remoteMessage);
    });
    
    // Check if the app was opened from a notification
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('App opened from quit state:', remoteMessage);
          setNotification(remoteMessage);
        }
      });
    
    return unsubscribe;
  }, []);
  
  return { notification };
};

/**
 * Phone authentication with Firebase
 */
export const usePhoneAuth = () => {
  const [confirm, setConfirm] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  
  // Send verification code
  const sendVerificationCode = async (phoneNumber) => {
    try {
      setLoading(true);
      setError(null);
      
      // Format phone number if needed
      const formattedPhoneNumber = phoneNumber.startsWith('+') 
        ? phoneNumber 
        : `+${phoneNumber}`;
      
      const confirmation = await auth().signInWithPhoneNumber(formattedPhoneNumber);
      setConfirm(confirmation);
      setLoading(false);
      return true;
    } catch (err) {
      console.error('Error sending verification code:', err);
      setError(err.message);
      setLoading(false);
      return false;
    }
  };
  
  // Verify OTP code
  const verifyCode = async (code) => {
    try {
      setLoading(true);
      setError(null);
      
      if (!confirm) {
        setError('No verification was sent. Please request a new code.');
        setLoading(false);
        return false;
      }
      
      await confirm.confirm(code);
      setLoading(false);
      return true;
    } catch (err) {
      console.error('Error verifying code:', err);
      setError(err.message);
      setLoading(false);
      return false;
    }
  };
  
  return {
    sendVerificationCode,
    verifyCode,
    loading,
    error,
    confirm: !!confirm,
  };
};

// Firebase Phone Authentication functions

/**
 * Initialize phone authentication with reCAPTCHA verification
 * @param {string} phoneNumber - Phone number with country code
 * @param {string} recaptchaToken - reCAPTCHA verification token
 * @returns {Promise<object>} - Returns verification ID or error
 */
export const sendPhoneVerificationCode = async (phoneNumber) => {
  try {
    if (!phoneNumber) {
      return { success: false, error: 'Phone number is required' };
    }
    const formattedPhone = phoneNumber.startsWith('+') 
      ? phoneNumber 
      : `+91${phoneNumber}`;
    const confirmation = await auth().signInWithPhoneNumber(formattedPhone);
    return {
      success: true,
      verificationId: confirmation.verificationId,
      expiresIn: 120
    };
  } catch (error) {
    console.error('Phone verification error:', error.code, error.message);
    return { success: false, error: error.message || 'Failed to send verification code' };
  }
};
/**
 * Verify phone number with OTP code
 * @param {string} verificationId - Verification ID received from sendPhoneVerificationCode
 * @param {string} code - 6-digit OTP code
 * @param {string} phoneNumber - Phone number with country code (optional)
 * @returns {Promise<object>} - Returns success status or error
 */
export const verifyPhoneNumber = async (verificationId, code, phoneNumber) => {
  try {
    // Validate inputs
    if (!verificationId) {
      return {
        success: false,
        error: 'Verification ID is required'
      };
    }

    if (!code) {
      return {
        success: false,
        error: 'OTP code is required'
      };
    }

    if (code.length !== 6) {
      return {
        success: false, 
        error: 'OTP code must be 6 digits'
      };
    }

    // Create a credential with the verification ID and code
    const credential = auth.PhoneAuthProvider.credential(verificationId, code);
    
    // Sign in with the credential
    const userCredential = await auth().signInWithCredential(credential);
    
    // Get the user token
    const firebaseToken = await userCredential.user.getIdToken();
    
    return {
      success: true,
      firebaseToken,
      firebaseUid: userCredential.user.uid,
      user: userCredential.user
    };
  } catch (error) {
    console.error('Phone verification error:', error);
    return {
      success: false,
      error: error.message || 'Failed to verify phone number'
    };
  }
};

export const registerWithFirebase = async (email ,password) =>{
  try {
    const userCredential = await auth().createUserWithEmailAndPassword(email, password);
    return userCredential.user.uid;
  } catch (error) {
    console.error('Firebase registration error:', error);
    throw new Error('Failed to register with Firebase');
  }
}
export default {
  initializeFirebase,
  useFirebaseMessaging,
  usePhoneAuth,
};

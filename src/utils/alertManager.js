import React from 'react';
import { Alert, Platform } from 'react-native';
import CustomAlert from '../components/common/CustomAlert';

// Global reference to the alert component
let alertRef = null;

// Alert state
let alertState = {
  visible: false,
  title: '',
  message: '',
  buttons: [],
  type: 'info',
  dismissable: true,
};

/**
 * Alert manager utility for showing alerts consistently across the app
 * Falls back to native Alert on platforms where CustomAlert is not available
 */
const alertManager = {
  /**
   * Set the alert component reference
   * @param {React.RefObject} ref - Reference to the alert component
   */
  setAlertRef: (ref) => {
    alertRef = ref;
  },

  /**
   * Show an alert
   * @param {string} title - Alert title
   * @param {string} message - Alert message
   * @param {Array} buttons - Array of button objects with text and onPress
   * @param {string} type - Alert type ('success', 'error', 'warning', 'info')
   * @param {boolean} dismissable - Whether the alert can be dismissed by tapping outside
   */
  showAlert: (title, message, buttons = [{ text: 'OK', onPress: () => {} }], type = 'info', dismissable = true) => {
    // If custom alert is not available, fall back to native Alert
    if (!alertRef) {
      Alert.alert(title, message, buttons.map(btn => ({
        text: btn.text,
        onPress: btn.onPress,
        style: btn.style
      })));
      return;
    }

    // Update alert state
    alertState = {
      visible: true,
      title,
      message,
      buttons,
      type,
      dismissable,
    };

    // Show the alert
    alertRef.current?.setAlertState(alertState);
  },

  /**
   * Hide the alert
   */
  hideAlert: () => {
    if (alertRef) {
      alertState.visible = false;
      alertRef.current?.setAlertState(alertState);
    }
  },

  /**
   * Show a success alert
   * @param {string} title - Alert title
   * @param {string} message - Alert message
   * @param {Array} buttons - Array of button objects with text and onPress
   */
  showSuccess: (title, message, buttons = [{ text: 'OK', onPress: () => {} }]) => {
    alertManager.showAlert(title, message, buttons, 'success');
  },

  /**
   * Show an error alert
   * @param {string} title - Alert title
   * @param {string} message - Alert message
   * @param {Array} buttons - Array of button objects with text and onPress
   */
  showError: (title, message, buttons = [{ text: 'OK', onPress: () => {} }]) => {
    alertManager.showAlert(title, message, buttons, 'error');
  },

  /**
   * Show a warning alert
   * @param {string} title - Alert title
   * @param {string} message - Alert message
   * @param {Array} buttons - Array of button objects with text and onPress
   */
  showWarning: (title, message, buttons = [{ text: 'OK', onPress: () => {} }]) => {
    alertManager.showAlert(title, message, buttons, 'warning');
  },

  /**
   * Show an info alert
   * @param {string} title - Alert title
   * @param {string} message - Alert message
   * @param {Array} buttons - Array of button objects with text and onPress
   */
  showInfo: (title, message, buttons = [{ text: 'OK' }]) => {
    alertManager.showAlert(title, message, buttons, 'info');
  },

  /**
   * Show a confirmation alert
   * @param {string} title - Alert title
   * @param {string} message - Alert message
   * @param {function} onConfirm - Function to call when confirmed
   * @param {function} onCancel - Function to call when cancelled
   * @param {string} confirmText - Text for confirm button
   * @param {string} cancelText - Text for cancel button
   */
  showConfirmation: (
    title,
    message,
    onConfirm,
    onCancel = () => {},
    confirmText = 'Confirm',
    cancelText = 'Cancel'
  ) => {
    const buttons = [
      {
        text: cancelText,
        onPress: onCancel,
        style: 'cancel',
      },
      {
        text: confirmText,
        onPress: onConfirm,
      },
    ];

    alertManager.showAlert(title, message, buttons, 'warning');
  },
};

export default alertManager;

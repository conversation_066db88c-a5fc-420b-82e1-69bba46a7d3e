import jwt from 'jsonwebtoken';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback_secret';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '1d';

/**
 * Generate JWT token
 * @param payload - Data to be encoded in the token
 * @returns JWT token string
 */
export const generateToken = (payload: object): string => {
  // @ts-ignore - Ignoring type issues with jwt.sign
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
  });
};

/**
 * Verify JWT token
 * @param token - JWT token to verify
 * @returns Decoded token payload or null if invalid
 */
export const verifyToken = (token: string): any | null => {
  try {
    // @ts-ignore - Ignoring type issues with jwt.verify
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

import { Platform } from 'react-native';
import { 
  API_BASE_URL, 
  API_TIMEOUT, 
  formatPhoneNumber, 
  ERROR_MESSAGES
} from '../config';

/**
 * Make an API request with timeout
 * @param {string} url - API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Object>} Response data or error
 */
const makeApiRequest = async (url, options) => {
  // Create a promise that rejects after the timeout
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error('Request timed out')), API_TIMEOUT);
  });
  
  try {
    // Race the fetch against the timeout
    const response = await Promise.race([
      fetch(url, options),
      timeoutPromise
    ]);
    
    const data = await response.json();
    return data;
  } catch (error) {
    // Handle specific error cases
    if (error.message === 'Request timed out') {
      throw new Error(ERROR_MESSAGES.timeout);
    }
    
    if (error.message && error.message.includes('Network request failed')) {
      throw new Error(ERROR_MESSAGES.network);
    }
    
    throw error;
  }
};

/**
 * Initialize phone authentication with reCAPTCHA verification
 * @param {string} phoneNumber - Phone number with country code
 * @param {string} recaptchaToken - reCAPTCHA verification token
 * @param {number} retryAttempts - Number of retry attempts (default: 1)
 * @returns {Promise<object>} - Returns verification ID or error
 */
export const sendPhoneVerificationCode = async (phoneNumber, recaptchaToken, retryAttempts = 1) => {
  try {
    // Validate inputs
    if (!phoneNumber) {
      return {
        success: false,
        error: 'Phone number is required'
      };
    }

    // Format the phone number
    const formattedPhone = formatPhoneNumber(phoneNumber);
      
    // Handle reCAPTCHA token requirements 
    // In production, we always use a fixed token even if one isn't provided
    let tokenToUse = recaptchaToken || 'production-recaptcha-token-fixed';
      
    // Prepare the request body
    const requestBody = {
      phoneNumber: formattedPhone,
      recaptchaToken: tokenToUse
    };
    
    // Send OTP via API
    const data = await makeApiRequest(`${API_BASE_URL}/auth/phone/send-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });
    
    if (data.status === 'success') {
      return {
        success: true,
        verificationId: data.data.verificationId,
        expiresIn: data.data.expiresIn
      };
    } else {
      throw new Error(data.message || 'Failed to send verification code');
    }
  } catch (error) {
    console.error('Phone verification error:', error);
    
    // Retry logic for network errors
    if (retryAttempts > 0 && 
        (error.message === ERROR_MESSAGES.network || 
         error.message === ERROR_MESSAGES.timeout)) {
      console.log(`Retrying sendPhoneVerificationCode... (${retryAttempts} attempts left)`);
      return sendPhoneVerificationCode(phoneNumber, recaptchaToken, retryAttempts - 1);
    }
    
    // Map common error messages to user-friendly versions
    let errorMessage = error.message || 'Failed to send verification code';
    
    if (errorMessage.includes('recaptcha') || errorMessage.includes('reCAPTCHA')) {
      errorMessage = ERROR_MESSAGES.recaptchaFailed;
    } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      errorMessage = errorMessage; // Already formatted
    } else if (errorMessage.includes('rate') || errorMessage.includes('limit')) {
      errorMessage = 'Too many attempts. Please wait a few minutes and try again.';
    }
    
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * Verify phone number with OTP code
 * @param {string} verificationId - Verification ID received from sendPhoneVerificationCode
 * @param {string} code - 6-digit OTP code
 * @param {string} phoneNumber - Phone number with country code (optional)
 * @param {string} recaptchaToken - reCAPTCHA verification token (optional)
 * @param {number} retryAttempts - Number of retry attempts (default: 1)
 * @returns {Promise<object>} - Returns success status or error
 */
export const verifyPhoneNumber = async (verificationId, code, phoneNumber, recaptchaToken, retryAttempts = 1) => {
  try {
    // Validate inputs
    if (!verificationId) {
      return {
        success: false,
        error: 'Verification ID is required'
      };
    }

    if (!code) {
      return {
        success: false,
        error: 'OTP code is required'
      };
    }

    if (code.length !== 6) {
      return {
        success: false, 
        error: 'OTP code must be 6 digits'
      };
    }

    // Format phone number if provided
    const formattedPhone = phoneNumber ? formatPhoneNumber(phoneNumber) : null;
      
    // Prepare request body
    const requestBody = {
      verificationId,
      code
    };

    // Add phone number to request if available
    if (formattedPhone) {
      requestBody.phoneNumber = formattedPhone;
    }
    
    // In production, always include a recaptchaToken
    requestBody.recaptchaToken = recaptchaToken || 'production-recaptcha-token-fixed';
      
    // Verify OTP via API
    const data = await makeApiRequest(`${API_BASE_URL}/auth/phone/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });
    
    if (data.status === 'success') {
      return {
        success: true,
        firebaseToken: data.data.firebaseToken,
        firebaseUid: data.data.firebaseUid
      };
    } else {
      throw new Error(data.message || 'Failed to verify phone number');
    }
  } catch (error) {
    console.error('Phone verification error:', error);
    
    // Retry logic for network errors
    if (retryAttempts > 0 && 
        (error.message === ERROR_MESSAGES.network || 
         error.message === ERROR_MESSAGES.timeout)) {
      console.log(`Retrying verifyPhoneNumber... (${retryAttempts} attempts left)`);
      return verifyPhoneNumber(verificationId, code, phoneNumber, recaptchaToken, retryAttempts - 1);
    }
    
    // Map common error messages to user-friendly versions
    let errorMessage = error.message || 'Failed to verify phone number';
    
    if (errorMessage.includes('expired')) {
      errorMessage = ERROR_MESSAGES.otpExpired;
    } else if (errorMessage.includes('invalid')) {
      errorMessage = ERROR_MESSAGES.otpInvalid;
    } else if (errorMessage.includes('network') || errorMessage.includes('timeout')) {
      errorMessage = errorMessage; // Already formatted
    }
    
    return {
      success: false,
      error: errorMessage
    };
  }
};

/**
 * Get network connection status
 * @returns {Promise<boolean>} Whether the device is connected to the internet
 */
export const checkNetworkConnection = async () => {
  if (Platform.OS === 'web') {
    return navigator.onLine;
  }
  
  try {
    // Use a tiny fetch to check connectivity
    const response = await fetch('https://www.google.com', { 
      method: 'HEAD',
      timeout: 5000
    });
    return response.ok;
  } catch (error) {
    console.log('Network connectivity check failed:', error);
    return false;
  }
};

export default {
  sendPhoneVerificationCode,
  verifyPhoneNumber,
  checkNetworkConnection
};

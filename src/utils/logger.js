/**
 * Logger utility for consistent logging throughout the application
 * 
 * Provides methods for different log levels with consistent formatting
 * and the ability to enable/disable logs for production builds
 */

// Configuration
const config = {
  // Enable logging in production (set to false for production builds)
  enableInProduction: false,
  
  // Minimum log level to show
  // 0 = debug, 1 = info, 2 = warn, 3 = error
  minLevel: __DEV__ ? 0 : 2,
  
  // Enable timestamps in logs
  showTimestamps: true,
  
  // Enable log groups (turn off if your console doesn't support groups)
  useGroups: true,
  
  // Enable network logging for specific API modules
  enabledNetworkLogs: {
    auth: true,    // Authentication API calls
    stock: true,   // Stock management API calls
    cart: true,    // Cart management API calls
    order: true,   // Order management API calls 
    enquiry: true, // Enquiry management API calls
  },
};

// Log levels
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
};

// Log level emoji prefixes
const LOG_PREFIXES = {
  [LOG_LEVELS.DEBUG]: '🔍',
  [LOG_LEVELS.INFO]: 'ℹ️',
  [LOG_LEVELS.WARN]: '⚠️',
  [LOG_LEVELS.ERROR]: '❌',
};

/**
 * Check if logging is enabled based on environment and config
 * @returns {boolean}
 */
const isLoggingEnabled = () => {
  if (__DEV__) return true;
  return config.enableInProduction;
};

/**
 * Format timestamp for logs
 * @returns {string}
 */
const getTimestamp = () => {
  if (!config.showTimestamps) return '';
  return `[${new Date().toISOString()}]`;
};

/**
 * Log message with specific level
 * @param {number} level - Log level
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const logWithLevel = (level, message, ...data) => {
  if (!isLoggingEnabled() || level < config.minLevel) return;
  
  const prefix = LOG_PREFIXES[level] || '';
  const timestamp = getTimestamp();
  
  if (data.length === 0) {
    console.log(`${prefix} ${timestamp} ${message}`);
  } else {
    if (config.useGroups) {
      console.group(`${prefix} ${timestamp} ${message}`);
      data.forEach(item => {
        if (item instanceof Error) {
          console.log(item.message);
          console.log(item.stack);
        } else {
          console.log(item);
        }
      });
      console.groupEnd();
    } else {
      console.log(`${prefix} ${timestamp} ${message}`, ...data);
    }
  }
};

/**
 * Log debug message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const debug = (message, ...data) => {
  logWithLevel(LOG_LEVELS.DEBUG, message, ...data);
};

/**
 * Log info message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const info = (message, ...data) => {
  logWithLevel(LOG_LEVELS.INFO, message, ...data);
};

/**
 * Log warning message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const warn = (message, ...data) => {
  logWithLevel(LOG_LEVELS.WARN, message, ...data);
};

/**
 * Log error message
 * @param {string} message - Log message
 * @param {any} data - Additional data to log
 */
const error = (message, ...data) => {
  logWithLevel(LOG_LEVELS.ERROR, message, ...data);
};

/**
 * Start a performance timer
 * @param {string} label - Timer label
 * @returns {function} Function to call when ending the timer
 */
const startTimer = (label) => {
  if (!isLoggingEnabled()) return () => {};
  
  const start = performance.now();
  return () => {
    const duration = performance.now() - start;
    logWithLevel(LOG_LEVELS.DEBUG, `⏱️ ${label}: ${duration.toFixed(2)}ms`);
    return duration;
  };
};

/**
 * Track API function call with timing
 * @param {string} module - API module name (auth, stock, etc.)
 * @param {string} functionName - Function name being called
 * @param {function} apiCall - Async function making the API call
 * @param {Array} args - Arguments to pass to the API call
 * @returns {Promise} - Result of the API call
 */
const trackApiCall = async (module, functionName, apiCall, ...args) => {
  // Skip if network logging is disabled for this module
  if (!isLoggingEnabled() || !config.enabledNetworkLogs[module]) {
    return apiCall(...args);
  }
  
  const requestId = 'xxxxxxxx'.replace(/[xy]/g, c => {
    const r = Math.random() * 16 | 0;
    return r.toString(16);
  });
  
  try {
    debug(`${module}.${functionName} called [${requestId}]`, args);
    const start = performance.now();
    
    const result = await apiCall(...args);
    
    const duration = performance.now() - start;
    info(`${module}.${functionName} succeeded [${requestId}] (${duration.toFixed(2)}ms)`, result);
    
    return result;
  } catch (error) {
    error(`${module}.${functionName} failed [${requestId}]`, error);
    throw error;
  }
};

export default {
  debug,
  info,
  warn,
  error,
  startTimer,
  trackApiCall,
  
  // Export configuration for runtime updates
  config,
};
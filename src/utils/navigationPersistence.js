/**
 * Navigation state persistence utility
 * Helps prevent navigation state loss during app refreshes
 */
import AsyncStorage from "@react-native-async-storage/async-storage"
import { Platform } from "react-native"
import logger from "./logger"

const NAVIGATION_STATE_KEY = "app:navigation:state"

/**
 * Utility to persist and restore navigation state
 */
const navigationPersistence = {
  /**
   * Save navigation state to AsyncStorage
   * @param {Object} state - Navigation state to save
   */
  async saveNavigationState(state) {
    try {
      if (!state) return

      // Only save if we have a valid state
      await AsyncStorage.setItem(NAVIGATION_STATE_KEY, JSON.stringify(state))
    } catch (error) {
      logger.error("Failed to save navigation state:", error)
    }
  },

  /**
   * Load navigation state from AsyncStorage
   * @returns {Object|null} - Saved navigation state or null
   */
  async loadNavigationState() {
    try {
      const savedState = await AsyncStorage.getItem(NAVIGATION_STATE_KEY)
      if (savedState) {
        return JSON.parse(savedState)
      }
      return null
    } catch (error) {
      logger.error("Failed to load navigation state:", error)
      return null
    }
  },

  /**
   * Clear saved navigation state
   */
  async clearNavigationState() {
    try {
      await AsyncStorage.removeItem(NAVIGATION_STATE_KEY)
    } catch (error) {
      logger.error("Failed to clear navigation state:", error)
    }
  },

  /**
   * Create a navigation state persistence handler
   * @returns {Object} - Navigation state persistence handlers
   */
  createNavigationPersistenceHandlers() {
    // Only enable on Android - iOS handles this better natively
    if (Platform.OS !== "android") {
      return {}
    }

    return {
      onStateChange: (state) => this.saveNavigationState(state),
      onReady: () => {
        // Clear the state when navigation is ready to prevent stale states
        this.clearNavigationState()
      },
    }
  },
}

export default navigationPersistence

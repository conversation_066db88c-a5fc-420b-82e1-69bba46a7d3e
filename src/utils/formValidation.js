/**
 * Form validation utilities for consistent validation across the app
 */

/**
 * Validate email format
 * @param {string} email - Email address to validate
 * @returns {boolean} - True if valid, false otherwise
 */
export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate phone number format (basic validation)
 * @param {string} phoneNumber - Phone number to validate
 * @returns {boolean} - True if valid, false otherwise
 */
export const isValidPhoneNumber = (phoneNumber) => {
  const phoneRegex = /^[+]?[0-9]{10,15}$/;
  return phoneRegex.test(phoneNumber);
};

/**
 * Validate GST number format (Indian GST number)
 * @param {string} gstNumber - GST number to validate
 * @returns {boolean} - True if valid, false otherwise
 */
export const isValidGSTNumber = (gstNumber) => {
  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
  return gstRegex.test(gstNumber);
};

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} - Result object with isValid flag and message
 */
export const validatePasswordStrength = (password) => {
  if (!password || password.length < 8) {
    return { 
      isValid: false, 
      message: 'Password must be at least 8 characters' 
    };
  }
  
  // Check for at least one number
  if (!/\d/.test(password)) {
    return { 
      isValid: false, 
      message: 'Password must contain at least one number' 
    };
  }
  
  // Check for at least one special character
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    return { 
      isValid: false, 
      message: 'Password must contain at least one special character' 
    };
  }
  
  return { isValid: true };
};

/**
 * Check if all required fields are present in an object
 * @param {Object} data - Data object to check
 * @param {Array<string>} requiredFields - List of required field names
 * @returns {Object} - Result object with isValid flag and missingField
 */
export const validateRequiredFields = (data, requiredFields) => {
  for (const field of requiredFields) {
    if (!data[field]) {
      const formattedFieldName = field
        .replace(/([A-Z])/g, ' $1')
        .toLowerCase();
      
      return {
        isValid: false,
        missingField: field,
        message: `Please enter ${formattedFieldName}`
      };
    }
  }
  
  return { isValid: true };
};
import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from './simpleLogger';

/**
 * Utility functions for persisting state in AsyncStorage
 */
const statePersistence = {
  /**
   * Save state to AsyncStorage
   * @param {string} key - Storage key
   * @param {any} value - Value to store (will be <PERSON>SO<PERSON> stringified)
   * @returns {Promise<void>}
   */
  saveState: async (key, value) => {
    try {
      const jsonValue = JSON.stringify(value);
      await AsyncStorage.setItem(key, jsonValue);
      logger.debug(`State saved: ${key}`);
    } catch (error) {
      logger.error(`Error saving state: ${key}`, error);
    }
  },

  /**
   * Load state from AsyncStorage
   * @param {string} key - Storage key
   * @param {any} defaultValue - Default value if key doesn't exist
   * @returns {Promise<any>} - Parsed value or defaultValue
   */
  loadState: async (key, defaultValue = null) => {
    try {
      const jsonValue = await AsyncStorage.getItem(key);
      if (jsonValue === null) {
        logger.debug(`No state found for key: ${key}, using default value`);
        return defaultValue;
      }
      logger.debug(`State loaded: ${key}`);
      return JSON.parse(jsonValue);
    } catch (error) {
      logger.error(`Error loading state: ${key}`, error);
      return defaultValue;
    }
  },

  /**
   * Remove state from AsyncStorage
   * @param {string} key - Storage key
   * @returns {Promise<void>}
   */
  removeState: async (key) => {
    try {
      await AsyncStorage.removeItem(key);
      logger.debug(`State removed: ${key}`);
    } catch (error) {
      logger.error(`Error removing state: ${key}`, error);
    }
  },

  /**
   * Clear all app state from AsyncStorage
   * @returns {Promise<void>}
   */
  clearAllState: async () => {
    try {
      // Get all keys that start with 'app:'
      const keys = await AsyncStorage.getAllKeys();
      const appKeys = keys.filter(key => key.startsWith('app:'));

      if (appKeys.length > 0) {
        await AsyncStorage.multiRemove(appKeys);
      }
      logger.debug('All app state cleared');
    } catch (error) {
      logger.error('Error clearing all app state', error);
    }
  }
};

export default statePersistence;

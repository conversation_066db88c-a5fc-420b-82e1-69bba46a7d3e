import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getUserCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
} from '../services/cart.service';

/**
 * Get user's cart items
 */
export const getCart = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const includeStock = req.query.includeStock !== 'false'; // Default to true

  const cart = await getUserCart(userId, includeStock);

  return res.status(200).json({
    status: 'success',
    data: cart,
  });
});

/**
 * Add item to cart
 */
export const addItemToCart = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user!.id;
  const { stockId, quantity } = req.body;

  const cartItem = await addToCart(userId, stockId, parseInt(quantity));

  return res.status(201).json({
    status: 'success',
    message: 'Item added to cart successfully',
    data: { cartItem },
  });
});

/**
 * Update cart item quantity
 */
export const updateCartItemQuantity = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user!.id;
  const { id } = req.params;
  const { quantity } = req.body;

  const cartItem = await updateCartItem(id, userId, parseInt(quantity));

  return res.status(200).json({
    status: 'success',
    message: 'Cart item updated successfully',
    data: { cartItem },
  });
});

/**
 * Remove item from cart
 */
export const removeCartItem = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user!.id;
  const { id } = req.params;

  const cartItem = await removeFromCart(id, userId);

  return res.status(200).json({
    status: 'success',
    message: 'Item removed from cart successfully',
    data: { cartItem },
  });
});

/**
 * Clear cart
 */
export const clearUserCart = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  const result = await clearCart(userId);

  return res.status(200).json({
    status: 'success',
    message: `${result.count} items removed from cart`,
    data: result,
  });
});

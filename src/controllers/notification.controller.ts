import { Request, Response, NextFunction } from 'express';
import { AppError, asyncHandler } from '../utils/errorHandler';
import {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
} from '../services/notification.service';

/**
 * Get user's notifications
 */
export const getNotifications = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;
  const { page, limit, unreadOnly } = req.query;

  const result = await getUserNotifications(
    userId,
    page ? parseInt(page as string) : undefined,
    limit ? parseInt(limit as string) : undefined,
    unreadOnly === 'true'
  );

  return res.status(200).json({
    status: 'success',
    data: result,
  });
});

/**
 * Mark notification as read
 */
export const markAsRead = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const notification = await markNotificationAsRead(id, userId);

  return res.status(200).json({
    status: 'success',
    message: 'Notification marked as read',
    data: { notification },
  });
});

/**
 * Mark all notifications as read
 */
export const markAllAsRead = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user!.id;

  const result = await markAllNotificationsAsRead(userId);

  return res.status(200).json({
    status: 'success',
    message: `${result.count} notifications marked as read`,
    data: result,
  });
});

/**
 * Delete notification
 */
export const deleteNotificationHandler = asyncHandler(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const notification = await deleteNotification(id, userId);

  return res.status(200).json({
    status: 'success',
    message: 'Notification deleted successfully',
    data: { notification },
  });
});

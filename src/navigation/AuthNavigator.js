import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';

// Import auth screens
import SplashScreen from '../screens/auth/SplashScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import OTPVerificationScreen from '../screens/auth/OTPVerificationScreen';
import RegistrationSuccessScreen from '../screens/auth/RegistrationSuccessScreen';
import SuccessScreen from '../screens/SuccessScreen';

const Stack = createStackNavigator();

/**
 * Authentication navigator for handling auth flow
 */
const AuthNavigator = () => {
  return (
    <Stack.Navigator
      initialRouteName="Splash"
      screenOptions={{
        headerShown: false,
        detachPreviousScreen: true // This helps prevent the Splash screen from interfering
      }}
    >
      <Stack.Screen name="Splash" component={SplashScreen} />
      <Stack.Screen name="OTPVerification" component={OTPVerificationScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Register" component={RegisterScreen} />
      <Stack.Screen name="RegistrationSuccess" component={RegistrationSuccessScreen} />
      <Stack.Screen
        name="Success"
        component={SuccessScreen}
        options={{
          headerShown: false,
          presentation: 'fullScreenModal'
        }}
      />
    </Stack.Navigator>
  );
};

export default AuthNavigator;

import apiClient from './apiClient';
import logger from '../utils/logger';

/**
 * Order API methods
 */
const orderApi = {
  /**
   * Place a new order from the current cart
   * @param {Object} orderData - Order data
   * @param {Object} orderData.shippingAddress - Shipping address
   * @param {string} orderData.shippingAddress.street - Street address
   * @param {string} orderData.shippingAddress.city - City
   * @param {string} orderData.shippingAddress.state - State
   * @param {string} orderData.shippingAddress.zipCode - Zip code
   * @param {string} orderData.shippingAddress.country - Country
   * @param {string} [orderData.notes] - Optional order notes
   * @returns {Promise} - Promise with order data
   */
  placeOrder: async (orderData) => {
    return logger.trackApiCall('order', 'placeOrder', async () => {
      const response = await apiClient.post('/orders', orderData);
      return response.data;
    });
  },

  /**
   * Get order history with optional filters
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number for pagination
   * @param {number} params.limit - Number of items per page
   * @param {string} params.status - Filter by order status
   * @param {string} params.startDate - Filter orders after this date (ISO format)
   * @param {string} params.endDate - Filter orders before this date (ISO format)
   * @returns {Promise} - Promise with order history data
   */
  getOrderHistory: async (params = {}) => {
    return logger.trackApiCall('order', 'getOrderHistory', async () => {
      const response = await apiClient.get('/orders', { params });
      return response.data;
    });
  },

  /**
   * Get order details by ID
   * @param {string} orderId - ID of the order to retrieve
   * @returns {Promise} - Promise with order details
   */
  getOrderDetails: async (orderId) => {
    return logger.trackApiCall('order', 'getOrderDetails', async () => {
      const response = await apiClient.get(`/orders/${orderId}`);
      return response.data;
    });
  },

  /**
   * Cancel an order
   * @param {string} orderId - ID of the order to cancel
   * @returns {Promise} - Promise with success message
   */
  cancelOrder: async (orderId) => {
    return logger.trackApiCall('order', 'cancelOrder', async () => {
      const response = await apiClient.post(`/orders/${orderId}/cancel`);
      return response.data;
    });
  },

  /**
   * Get all orders (Admin only)
   * @param {Object} params - Query parameters
   * @returns {Promise} - Promise with all orders data
   */
  getAllOrders: async (params = {}) => {
    return logger.trackApiCall('order', 'getAllOrders', async () => {
      const response = await apiClient.get('/admin/orders', { params });
      return response.data;
    });
  },

  /**
   * Update order status (Admin only)
   * @param {string} orderId - ID of the order to update
   * @param {string} status - New status
   * @returns {Promise} - Promise with updated order data
   */
  updateOrderStatus: async (orderId, status) => {
    return logger.trackApiCall('order', 'updateOrderStatus', async () => {
      const response = await apiClient.put(`/admin/orders/${orderId}/status`, { status });
      return response.data;
    });
  },
};

export default orderApi;

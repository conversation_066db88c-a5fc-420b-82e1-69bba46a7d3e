import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config';
import { getAuth } from 'firebase/auth';

const API_URL = API_BASE_URL;

/**
 * Utility function to generate request ID
 * @returns {string} UUID for request tracking
 */
const generateRequestId = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.floor(Math.random() * 16);
    // For 'x', use random value; for 'y', use values 8-11 to comply with RFC4122
    const v = c === 'x' ? r : (Math.floor(Math.random() * 4) + 8);
    return v.toString(16);
  });
};

/**
 * Format data for logging (truncate large payloads)
 * @param {any} data - The data to format
 * @returns {any} Formatted data for logging
 */
const formatLogData = (data) => {
  if (!data) {return null;}

  // For files or FormData, just indicate the type
  if (data instanceof FormData) {return '[FormData]';}

  // For objects, stringify but limit size
  if (typeof data === 'object') {
    const stringified = JSON.stringify(data);
    if (stringified.length > 1000) {
      return stringified.substring(0, 1000) + '... [truncated]';
    }
    return data;
  }

  return data;
};

/**
 * API client using native fetch instead of axios
 */
const apiClient = {
  /**
   * Make a GET request
   * @param {string} url - The URL to request (without base URL)
   * @param {Object} options - Additional options
   * @returns {Promise} - Promise with response data
   */
  async get(url, options = {}) {
    return this.request(url, 'GET', null, options);
  },

  /**
   * Make a POST request
   * @param {string} url - The URL to request (without base URL)
   * @param {Object} data - The data to send
   * @param {Object} options - Additional options
   * @returns {Promise} - Promise with response data
   */
  async post(url, data, options = {}) {
    return this.request(url, 'POST', data, options);
  },

  /**
   * Make a PUT request
   * @param {string} url - The URL to request (without base URL)
   * @param {Object} data - The data to send
   * @param {Object} options - Additional options
   * @returns {Promise} - Promise with response data
   */
  async put(url, data, options = {}) {
    return this.request(url, 'PUT', data, options);
  },

  /**
   * Make a DELETE request
   * @param {string} url - The URL to request (without base URL)
   * @param {Object} options - Additional options
   * @returns {Promise} - Promise with response data
   */
  async delete(url, options = {}) {
    return this.request(url, 'DELETE', null, options);
  },

  /**
   * Make a request with the specified method
   * @param {string} url - The URL to request (without base URL)
   * @param {string} method - The HTTP method
   * @param {Object} data - The data to send
   * @param {Object} options - Additional options
   * @returns {Promise} - Promise with response data
   */
  async request(url, method, data, options = {}) {
    const requestId = generateRequestId();
    const requestTime = new Date();
    const { params = {} } = options;

    // Build URL with query parameters
    let fullUrl = `${API_URL}${url}`;
    if (Object.keys(params).length > 0) {
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value);
        }
      });
      const queryString = queryParams.toString();
      if (queryString) {
        fullUrl += `?${queryString}`;
      }
    }

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add auth token if available
    const token = await AsyncStorage.getItem('auth_token');
    if (token) {
      headers.Authorization = `Bearer ${token}`;
      
      // Log that we're including the auth token (for debugging)
      logger.debug('Including auth token in request', {
        url: fullUrl,
        tokenExists: !!token,
        tokenPreview: token ? `${token.substring(0, 10)}...` : null
      });
    } else {
      logger.warn('No auth token available for request', { url: fullUrl });
    }

    // Prepare request options
    const fetchOptions = {
      method,
      headers,
      ...(data && { body: JSON.stringify(data) }),
    };

    // Log the request
    logger.info(`API Request [${requestId}]`, {
      method,
      url: fullUrl,
      headers: { ...headers, Authorization: headers.Authorization ? 'Bearer [REDACTED]' : undefined },
      params,
      body: formatLogData(data),
      timestamp: requestTime.toISOString(),
    });

    try {
      // Make the request
      const response = await fetch(fullUrl, fetchOptions);
      const responseTime = new Date();
      const duration = responseTime - requestTime;

      // Parse response data
      let responseData;
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }

      // Log the response
      logger.info(`API Response [${requestId}]`, {
        method,
        url,
        status: `${response.status} ${response.statusText}`,
        duration: `${duration}ms`,
        data: formatLogData(responseData),
        timestamp: responseTime.toISOString(),
      });

      // Handle 401 Unauthorized (token expired)
      if (response.status === 401) {
        logger.warn('Received 401 unauthorized, attempting token refresh');
        const refreshed = await this.handleTokenRefresh();
        if (refreshed) {
          logger.info('Token refreshed successfully, retrying request');
          // Retry the request with new token
          return this.request(url, method, data, options);
        } else {
          logger.warn('Token refresh failed, proceeding with error response');
        }
      }

      // If response is not ok, throw error
      if (!response.ok) {
        throw {
          response: {
            status: response.status,
            statusText: response.statusText,
            data: responseData,
          },
          message: `Request failed with status ${response.status}`,
        };
      }

      return responseData;
    } catch (error) {
      const responseTime = new Date();
      const duration = responseTime - requestTime;

      // Log the error
      logger.error(`API Error [${requestId}]`, {
        method,
        url,
        status: error.response?.status || 'UNKNOWN',
        statusText: error.response?.statusText || '',
        duration: `${duration}ms`,
        data: formatLogData(error.response?.data),
        message: error.message,
        stack: __DEV__ ? error.stack : undefined,
        timestamp: responseTime.toISOString(),
      });

      throw error;
    }
  },

  /**
   * Handle token refresh when receiving a 401 response
   * @returns {Promise<boolean>} - Whether the token was successfully refreshed
   */
  async handleTokenRefresh() {
    try {
      logger.debug('Attempting to refresh Firebase token');
      
      // For Firebase, we need to refresh the ID token
      const auth = getAuth();
      const currentUser = auth.currentUser;
      
      if (!currentUser) {
        logger.warn('No current user found in Firebase Auth, cannot refresh token');
        await AsyncStorage.removeItem('auth_token');
        return false;
      }
      
      // Force token refresh
      const newIdToken = await currentUser.getIdToken(true);
      
      if (newIdToken) {
        logger.info('Successfully refreshed Firebase ID token');
        await AsyncStorage.setItem('auth_token', newIdToken);
        return true;
      } else {
        throw new Error('Failed to get new ID token');
      }
    } catch (error) {
      logger.error('Token refresh failed:', error);
      // Refresh failed, clear auth data
      await AsyncStorage.removeItem('auth_token');
      return false;
    }
  }
};

export default apiClient;
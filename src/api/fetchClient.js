import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from '../utils/logger';
import { API_BASE_URL } from '../config';

const API_URL = API_BASE_URL;

/**
 * Generate a unique request ID for tracking
 */
const generateRequestId = () => {
  return 'req_' + Math.random().toString(36).substring(2, 15);
};

/**
 * Format data for logging (truncate large payloads)
 */
const formatLogData = (data) => {
  if (!data) return null;
  
  if (data instanceof FormData) return '[FormData]';
  
  if (typeof data === 'object') {
    try {
      const stringified = JSON.stringify(data);
      if (stringified.length > 1000) {
        return stringified.substring(0, 1000) + '... [truncated]';
      }
      return data;
    } catch (e) {
      return '[Object that cannot be stringified]';
    }
  }
  
  return data;
};

/**
 * Flag to prevent multiple simultaneous token refresh attempts
 */
let isRefreshing = false;
let refreshPromise = null;

/**
 * Simple fetch client with authentication and logging
 */
const fetchClient = {
  /**
   * Make a fetch request
   */
  async request(endpoint, options = {}) {
    const requestId = generateRequestId();
    const url = `${API_URL}${endpoint}`;
    const startTime = Date.now();
    
    // Default headers
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    };
    
    // Add auth token if available
    try {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    } catch (error) {
      logger.error('Failed to get auth token', error);
    }
    
    // Prepare request options
    const fetchOptions = {
      ...options,
      headers,
    };
    
    // Log the request
    logger.info(`API Request [${requestId}]`, {
      method: fetchOptions.method || 'GET',
      url,
      headers: fetchOptions.headers,
      body: fetchOptions.body ? formatLogData(fetchOptions.body) : undefined,
    });
    
    try {
      // Make the request
      const response = await fetch(url, fetchOptions);
      const duration = Date.now() - startTime;
      
      // Try to parse response as JSON
      let data;
      const contentType = response.headers.get('content-type');
      
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        const text = await response.text();
        
        if (text.includes('<!DOCTYPE html>')) {
          logger.error(`API Response [${requestId}] - Received HTML instead of JSON`, {
            status: response.status,
            duration: `${duration}ms`,
            preview: text.substring(0, 200) + '...',
          });
          
          throw new Error('Server returned HTML instead of JSON. The server might be down or misconfigured.');
        }
        
        try {
          data = JSON.parse(text);
        } catch (e) {
          data = text;
        }
      }
      
      // Log the response
      logger.info(`API Response [${requestId}]`, {
        status: response.status,
        duration: `${duration}ms`,
        data: formatLogData(data),
      });
      
      // Handle error responses
      if (!response.ok) {
        const error = new Error(data?.message || `API error: ${response.status}`);
        error.status = response.status;
        error.data = data;
        throw error;
      }
      
      return data;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Log the error
      logger.error(`API Error [${requestId}]`, {
        method: fetchOptions.method || 'GET',
        url,
        duration: `${duration}ms`,
        message: error.message,
        stack: __DEV__ ? error.stack : undefined,
      });
      
      // Handle token refresh on 401 errors (unauthorized)
      if (error.status === 401 && endpoint !== '/auth/refresh') {
        try {
          // Prevent multiple simultaneous refresh attempts
          if (isRefreshing) {
            await refreshPromise;
            // Retry the original request after refresh completes
            return this.request(endpoint, options);
          }
          
          isRefreshing = true;
          refreshPromise = this.refreshToken();
          
          const refreshResult = await refreshPromise;
          
          if (refreshResult.success) {
            // Retry the original request with new token
            return this.request(endpoint, options);
          } else {
            // Refresh failed, don't clear tokens here - let AuthContext handle it
            throw error;
          }
        } catch (refreshError) {
          logger.error('Token refresh failed', refreshError);
          // Don't clear tokens here - let AuthContext handle logout
          throw error;
        } finally {
          isRefreshing = false;
          refreshPromise = null;
        }
      }
      
      throw error;
    }
  },

  /**
   * Refresh authentication token
   */
  async refreshToken() {
    try {
      const refreshToken = await AsyncStorage.getItem('refresh_token');
      if (!refreshToken) {
        return { success: false, error: 'No refresh token' };
      }
      
      const refreshResponse = await fetch(`${API_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });
      
      if (!refreshResponse.ok) {
        return { success: false, error: 'Refresh request failed' };
      }
      
      const refreshData = await refreshResponse.json();
      const newToken = refreshData.data?.token;
      const newRefreshToken = refreshData.data?.refreshToken;
      
      if (newToken) {
        await AsyncStorage.setItem('auth_token', newToken);
        if (newRefreshToken) {
          await AsyncStorage.setItem('refresh_token', newRefreshToken);
        }
        return { success: true };
      }
      
      return { success: false, error: 'No token in response' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  },
  
  async get(endpoint, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'GET',
    });
  },
  
  async post(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
    });
  },
  
  async put(endpoint, data, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },
  
  async delete(endpoint, options = {}) {
    return this.request(endpoint, {
      ...options,
      method: 'DELETE',
    });
  },
};

export default fetchClient;
import apiClient from './apiClient';
import logger from '../utils/logger';

/**
 * Stock API methods
 */
const stockApi = {
  /**
   * Get available stock with optional filters
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number for pagination
   * @param {number} params.limit - Number of items per page
   * @param {string} params.type - Filter by paper type (e.g., "NS", "GY")
   * @param {number} params.gsm - Filter by GSM value
   * @param {number} params.bf - Filter by Bursting Factor
   * @returns {Promise} - Promise with stock data
   */
  getStock: async (params = {}) => {
    return logger.trackApiCall('stock', 'getStock', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get('/stock', { params });
    });
  },

  /**
   * Add new stock (Admin only)
   * @param {Object} stockData - Stock data to add
   * @param {string} stockData.type - Paper type (e.g., "NS", "GY")
   * @param {number} stockData.gsm - GSM value
   * @param {number} stockData.bf - Bursting Factor
   * @param {number} stockData.rollsAvailable - Number of rolls available
   * @param {number} stockData.pricePerRoll - Price per roll
   * @returns {Promise} - Promise with added stock data
   */
  addStock: async (stockData) => {
    return logger.trackApiCall('stock', 'addStock', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.post('/stock', stockData);
    });
  },

  /**
   * Update existing stock (Admin only)
   * @param {string} stockId - ID of the stock to update
   * @param {Object} stockData - Updated stock data
   * @returns {Promise} - Promise with updated stock data
   */
  updateStock: async (stockId, stockData) => {
    return logger.trackApiCall('stock', 'updateStock', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.put(`/stock/${stockId}`, stockData);
    });
  },

  /**
   * Delete stock (Admin only)
   * @param {string} stockId - ID of the stock to delete
   * @returns {Promise} - Promise with success message
   */
  deleteStock: async (stockId) => {
    return logger.trackApiCall('stock', 'deleteStock', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.delete(`/stock/${stockId}`);
    });
  },
};

export default stockApi;

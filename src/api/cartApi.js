import apiClient from './apiClient';
import logger from '../utils/logger';

/**
 * Cart API methods
 */
const cartApi = {
  /**
   * Get current user's cart items
   * @returns {Promise} - Promise with cart data
   */
  getCart: async () => {
    return logger.trackApiCall('cart', 'getCart', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.get('/cart');
    });
  },

  /**
   * Add item to cart
   * @param {Object} cartItem - Cart item to add
   * @param {string} cartItem.stockId - ID of the stock to add
   * @param {number} cartItem.quantity - Quantity to add
   * @returns {Promise} - Promise with added cart item data
   */
  addToCart: async (cartItem) => {
    return logger.trackApiCall('cart', 'addToCart', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.post('/cart', cartItem);
    });
  },

  /**
   * Update cart item quantity
   * @param {string} cartItemId - ID of the cart item to update
   * @param {number} quantity - New quantity
   * @returns {Promise} - Promise with updated cart item data
   */
  updateCartItem: async (cartItemId, quantity) => {
    return logger.trackApiCall('cart', 'updateCartItem', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.put(`/cart/${cartItemId}`, { quantity });
    });
  },

  /**
   * Remove item from cart
   * @param {string} cartItemId - ID of the cart item to remove
   * @returns {Promise} - Promise with success message
   */
  removeCartItem: async (cartItemId) => {
    return logger.trackApiCall('cart', 'removeCartItem', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.delete(`/cart/${cartItemId}`);
    });
  },

  /**
   * Clear all items from cart
   * @returns {Promise} - Promise with success message
   */
  clearCart: async () => {
    return logger.trackApiCall('cart', 'clearCart', async () => {
      // With our new fetch-based client, the response is already the data
      return await apiClient.delete('/cart/clear');
    });
  },
};

export default cartApi;

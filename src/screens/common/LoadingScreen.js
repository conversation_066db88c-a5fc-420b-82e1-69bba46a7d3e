import React from 'react';
import { View, ActivityIndicator, StyleSheet, Text } from 'react-native';
import { colors } from '../../theme';

/**
 * LoadingScreen component
 * Displays a full-screen loading indicator with optional message
 */
const LoadingScreen = ({ message = 'Loading...' }) => {
  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={colors.primary} />
      <Text style={styles.message}>{message}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background || '#ffffff',
    padding: 20,
  },
  message: {
    marginTop: 20,
    fontSize: 16,
    color: colors.text || '#333333',
    textAlign: 'center',
  },
});

export default LoadingScreen;
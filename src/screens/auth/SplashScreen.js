"use client"

import { useEffect } from "react"
import { View, Image, Text, StyleSheet } from "react-native"
import { colors } from "../../theme/colors"
import { useAuth } from "../../store/auth/AuthContext"
import StatusBarManager from "../../components/common/StatusBarManager"
import { spacing } from "../../theme/spacing"
import { textStyles } from "../../theme/typography"

/**
 * Splash screen with Rukmini Ventures logo
 */
const SplashScreen = ({ navigation }) => {
  const { isAuthenticated, isLoading } = useAuth()

  // Navigate to the appropriate screen after a delay
  useEffect(() => {
    console.log("SplashScreen useEffect - Auth State:", { isAuthenticated, isLoading });
    
    // Store whether the navigation has been performed
    const navigationRef = { hasNavigated: false };
    
    const timer = setTimeout(() => {
      if (!isLoading && !navigationRef.hasNavigated) {
        try {
          // Get the current navigation state
          const navigationState = navigation.getState();
          const currentRouteName = navigationState?.routes?.[navigationState.index]?.name;
          
          console.log("SplashScreen timeout - Current route:", currentRouteName);
          console.log("SplashScreen timeout - Current Auth State:", { isAuthenticated });
          
          // Only navigate if we're still on the Splash screen
          if (currentRouteName === 'Splash') {
            if (isAuthenticated) {
              console.log("Navigating to Main from SplashScreen");
              navigation.replace("Main");
            } else {
              console.log("Navigating to Login from SplashScreen");
              navigation.replace("Login");
            }
            navigationRef.hasNavigated = true;
          } else {
            console.log("Skipping SplashScreen navigation as we're already on:", currentRouteName);
          }
        } catch (e) {
          console.error("Error during SplashScreen navigation:", e);
        }
      }
    }, 2000) // 2 seconds delay

    return () => clearTimeout(timer)
  }, [isLoading, isAuthenticated, navigation])

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.white} barStyle="dark-content" />
      <Image
        source={require("../../assets/images/rukmini_ventures_logo.png")}
        style={styles.logo}
        resizeMode="contain"
      />
      <Text style={styles.welcomeText}>Welcome to</Text>
      <Text style={styles.companyText}>Rukmini Ventures</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.white,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: spacing.medium,
  },
  welcomeText: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginTop: spacing.large,
  },
  companyText: {
    ...textStyles.heading1,
    color: colors.primary,
    fontWeight: "bold",
  },
})

export default SplashScreen

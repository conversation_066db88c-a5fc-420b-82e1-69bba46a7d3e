/* eslint-disable react/no-unstable-nested-components */
"use client"

import { useState, useEffect, useRef } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from "react-native"
import { colors } from "../../theme/colors"
import { textStyles } from "../../theme/typography"
import { spacing } from "../../theme/spacing"
import Header from "../../components/common/Header"
import Input from "../../components/common/Input"
import Button from "../../components/common/Button"
import Dropdown from "../../components/common/Dropdown"
import StatusBarManager from "../../components/common/StatusBarManager"
import OTPInput from "../../components/common/OTPInput"
import { useAuth } from "../../store/auth/AuthContext"
import { isValidEmail, isValidPhoneNumber, isValidGSTNumber, validateRequiredFields } from "../../utils/formValidation"
import errorHandler from "../../utils/errorHandler"
import alertManager from "../../utils/alertManager"
import { API_BASE_URL } from "../../config"
import { FirebaseRecaptcha, useFirebaseRecaptcha } from "../../utils/recaptcha"
import { registerWithFirebase, sendPhoneVerificationCode, verifyPhoneNumber } from "../../utils/firebase"
import { getAuth } from "@react-native-firebase/auth"

/**
 * Registration Form component for collecting user data
 */
const RegistrationForm = ({ formData, setFormData, onSubmit }) => {
  const [isLoading, setIsLoading] = useState(false)

  // Payment terms options
  const paymentTermsOptions = [
    { label: "Immediate", value: "IMMEDIATE" },
    { label: "30 Days", value: "30_DAYS" },
    { label: "60 Days", value: "60_DAYS" },
  ]

  // Handle form input changes
  const handleChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  // Validate form data
  const validateForm = () => {
    // Check if all required fields are filled
    const requiredFields = [
      "gstNumber",
      "companyName",
      "contactPerson",
      "contactNumber",
      "email",
      "password",
      "paymentTerms",
    ]

    const requiredFieldsResult = validateRequiredFields(formData, requiredFields)
    if (!requiredFieldsResult.isValid) {
      alertManager.showError("Error", requiredFieldsResult.message)
      return false
    }

    // Validate GST number format
    if (!isValidGSTNumber(formData.gstNumber)) {
      alertManager.showError("Error", "Please enter a valid GST number")
      return false
    }

    // Validate email format
    if (!isValidEmail(formData.email)) {
      alertManager.showError("Error", "Please enter a valid email address")
      return false
    }

    // Validate phone number format
    if (!isValidPhoneNumber(formData.contactNumber)) {
      alertManager.showError("Error", "Please enter a valid phone number")
      return false
    }

    return true
  }

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return

    setIsLoading(true)
    try {
      await onSubmit()
    } catch (error) {
      console.error('Form submission error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Company Details</Text>

          <Input
            label="GST Number"
            value={formData.gstNumber}
            onChangeText={(value) => handleChange("gstNumber", value)}
            placeholder="Enter GST number"
            autoCapitalize="characters"
          />

          <Input
            label="Company Name"
            value={formData.companyName}
            onChangeText={(value) => handleChange("companyName", value)}
            placeholder="Enter company name"
          />

          <Input
            label="Contact Person Name"
            value={formData.contactPerson}
            onChangeText={(value) => handleChange("contactPerson", value)}
            placeholder="Enter contact person name"
          />

          <Input
            label="Company Contact Number"
            value={formData.contactNumber}
            onChangeText={(value) => handleChange("contactNumber", value)}
            placeholder="Enter contact number"
            keyboardType="phone-pad"
          />

          <Input
            label="Company Contact Email"
            value={formData.email}
            onChangeText={(value) => handleChange("email", value)}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
          />

          <Input
            label="Password"
            value={formData.password}
            onChangeText={(value) => handleChange("password", value)}
            placeholder="Enter password"
            secureTextEntry
          />

          <Dropdown
            label="Payment Terms"
            options={paymentTermsOptions}
            selectedValue={formData.paymentTerms}
            onValueChange={(value) => handleChange("paymentTerms", value)}
            placeholder="Select payment terms"
          />

          <Button
            title="Proceed to OTP Verification"
            onPress={handleSubmit}
            loading={isLoading}
            style={styles.button}
          />
        </View>
      </ScrollView>
    </View>
  )
}

/**
 * Email OTP Verification component
 */
const EmailOTPVerification = ({ email, onVerificationSuccess, onResendRequest }) => {
  const [otp, setOtp] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)

  // Timer for OTP resend
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  // Format time for display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Handle OTP verification
  const handleVerify = async () => {
    if (otp.length !== 6) {
      alertManager.showError('Error', 'Please enter the complete 6-digit OTP');
      return;
    }

    setIsLoading(true);
    try {
      // Verify email OTP
      const response = await fetch(`${API_BASE_URL}/auth/verify-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          otp,
        }),
      });
      const data = await response.json();
      console.log("Email verification response:", data);

      if (data.status === 'success') {
        // Email verified successfully
        onVerificationSuccess();
      } else {
        throw new Error(data.message || 'Failed to verify email OTP');
      }
    } catch (error) {
      console.error('Email OTP verification error:', error);
      errorHandler.handleApiError(error, {
        context: "email_verification",
        fallbackMessage: "Failed to verify email. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Handle resend OTP
  const handleResend = async () => {
    if (!canResend) return;

    setIsLoading(true);
    try {
      await onResendRequest();
      // Reset countdown
      setCountdown(60);
      setCanResend(false);
    } catch (error) {
      console.error('Resend OTP error:', error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.otpContainer}>
        <Text style={styles.verificationTitle}>Email Verification</Text>
        <Text style={styles.verificationText}>
          Please enter the 6-digit code sent to {email}
        </Text>

        <OTPInput
          length={6}
          onCodeFilled={setOtp}
          autoFocus
        />

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>
            Resend Code in: {formatTime(countdown)}
          </Text>

          <TouchableOpacity
            onPress={handleResend}
            disabled={!canResend || isLoading}
          >
            <Text style={[
              styles.resendButton,
              (!canResend || isLoading) && styles.disabledText,
            ]}>
              Resend Code
            </Text>
          </TouchableOpacity>
        </View>

        <Button
          title="Verify Email"
          onPress={handleVerify}
          loading={isLoading}
          style={styles.button}
        />
      </View>
    </View>
  )
}

/**
 * Registration screen for new users with complete OTP verification flow
 */
const RegisterScreen = ({ navigation }) => {
  // Registration steps
  const STEPS = {
    FORM: 'form',
    EMAIL_OTP: 'email_otp',
    PHONE_OTP: 'phone_otp',
  }

  // Current step in registration process
  const [currentStep, setCurrentStep] = useState(STEPS.FORM)

  // Form data state
  const [formData, setFormData] = useState({
    gstNumber: "22AAAAA0000A1Z5",
    companyName: "Test Company Pvt Ltd",
    contactPerson: "John Doe",
    contactNumber: "9694169828",
    email: "<EMAIL>",
    password: "Test@1234",
    paymentTerms: "IMMEDIATE",
  })

  // Verification states
  const [emailVerified, setEmailVerified] = useState(false)
  const [phoneVerified, setPhoneVerified] = useState(false)
  const [verificationId, setVerificationId] = useState(null)
  
/**
 * Phone OTP Verification component
 */
const PhoneOTPVerification = ({ phoneNumber, verificationId, onVerificationSuccess, onResendRequest }) => {
  const [otp, setOtp] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)

  // Timer for OTP resend
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [countdown]);

  // Format time for display
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  // Handle OTP verification
  const handleVerify = async () => {
    if (otp.length !== 6) {
      alertManager.showError('Error', 'Please enter the complete 6-digit OTP');
      return;
    }

    setIsLoading(true);
    try {
      // Verify phone OTP using the Firebase verification function
      const result = await verifyPhoneNumber(verificationId, otp, phoneNumber);

      if (result.success) {
        // Phone verified successfully
        onVerificationSuccess();
      } else {
        // Show specific error message from the backend if available
        const errorMessage = result.error || 'Failed to verify phone OTP';
        
        // Show more user-friendly error message for common errors
        if (errorMessage.includes('expired')) {
          alertManager.showError('Error', 'The verification code has expired. Please request a new one.');
        } else if (errorMessage.includes('invalid')) {
          alertManager.showError('Error', 'Invalid verification code. Please check and try again.');
        } else {
          alertManager.showError('Error', errorMessage);
        }
      }
    } catch (error) {
      console.error('Phone OTP verification error:', error);
      errorHandler.handleApiError(error, {
        context: "phone_verification",
        fallbackMessage: "Failed to verify phone number. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  }

  // Handle resend OTP
  const handleResend = async () => {
    if (!canResend) return;

    setIsLoading(true);
    try {
      await onResendRequest();
      // Reset countdown
      setCountdown(60);
      setCanResend(false);
    } catch (error) {
      console.error('Resend OTP error:', error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.otpContainer}>
        <Text style={styles.verificationTitle}>Phone Verification</Text>
        <Text style={styles.verificationText}>
          Please enter the 6-digit code sent to {phoneNumber}
        </Text>

        <OTPInput
          length={6}
          onCodeFilled={setOtp}
          autoFocus
        />

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>
            Resend Code in: {formatTime(countdown)}
          </Text>

          <TouchableOpacity
            onPress={handleResend}
            disabled={!canResend || isLoading}
          >
            <Text style={[
              styles.resendButton,
              (!canResend || isLoading) && styles.disabledText,
            ]}>
              Resend Code
            </Text>
          </TouchableOpacity>
        </View>

        <Button
          title="Verify Phone"
          onPress={handleVerify}
          loading={isLoading}
          style={styles.button}
        />
      </View>
    </View>
  )
}

  // Render the appropriate component based on current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case STEPS.FORM:
        return (
          <RegistrationForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleEmailVerificationSuccess}
          />
        )
      case STEPS.EMAIL_OTP:
        return (
          <EmailOTPVerification
            email={formData.email}
            onVerificationSuccess={handleEmailVerificationSuccess}
            onResendRequest={handleResendEmailOTP}
          />
        )
      case STEPS.PHONE_OTP:
        return (
          <PhoneOTPVerification
            phoneNumber={formData.contactNumber}
            verificationId={verificationId}
            onVerificationSuccess={handlePhoneVerificationSuccess}
            onResendRequest={handleResendPhoneOTP}
          />
        )
      default:
        return <RegistrationForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleEmailVerificationSuccess}
        />
    }
  }

  // Handle proceeding to email verification
  const handleProceedToEmailVerification = async () => {
    try {
      // Request email OTP
      console.log("sending otp to email", formData.email);
      const response = await fetch(`${API_BASE_URL}/auth/send-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      });
      const data = await response.json()
      console.log("Email OTP API response:", data);

      if (data.status === 'success') {
        // Move to email OTP verification step
        setCurrentStep(STEPS.EMAIL_OTP);
      } else {
        throw new Error(data.message || 'Failed to send OTP')
      }
    } catch (error) {
      console.error('Email OTP request error:', error)
      errorHandler.handleApiError(error, {
        context: "email_verification",
        fallbackMessage: "Failed to send email verification code. Please try again.",
      })
    }
  }

  // Handle email verification success
  const handleEmailVerificationSuccess = async () => {
    setEmailVerified(true);

    try {
    
      
      // Request phone OTP with reCAPTCHA token
      const result = await sendPhoneVerificationCode(formData.contactNumber);
      
      if (result.success) {
        // Store verification ID for phone verification
        setVerificationId(result.verificationId);

        // Move to phone OTP verification step
        setCurrentStep(STEPS.PHONE_OTP);
      } else {
        throw new Error(result.error || 'Failed to send phone OTP');
      }
    } catch (error) {
      console.error('Phone OTP request error:', error);
      errorHandler.handleApiError(error, {
        context: "phone_verification",
        fallbackMessage: "Failed to send phone verification code. Please try again.",
      });
    }
  }

  // Handle phone verification success
  const handlePhoneVerificationSuccess = async () => {
    setPhoneVerified(true);

    try {
      // Submit complete registration data
      // register with firebase to get firebase uid and sendit in request
      try{
        const firebaseUid = await registerWithFirebase(formData.email, formData.password);
        formData.firebaseUid = firebaseUid;

      }catch(error){
        console.error('Firebase registration error:', error);
        // if already registered, get the uid
        if(error.message && error.message.includes('auth/email-already-in-use')){
          const userCredential = await getAuth().signInWithEmailAndPassword(formData.email, formData.password);
          formData.firebaseUid = userCredential.user.uid;
        }
        
      }

      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      const data = await response.json()

      if (data.status === 'success') {
        // Navigate to registration success screen
        navigation.navigate('RegistrationSuccess');
      } else {
        throw new Error(data.message || 'Registration failed')
      }
    } catch (error) {
      console.error('Registration error:', error)
      errorHandler.handleApiError(error, {
        context: "registration",
        fallbackMessage: "Failed to complete registration. Please try again.",
      })
    }
  }

  // Handle resend email OTP
  const handleResendEmailOTP = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/send-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      });
      const data = await response.json()

      if (data.status === 'success') {
        alertManager.showSuccess('Success', 'OTP has been resent to your email');
      } else {
        throw new Error(data.message || 'Failed to resend OTP')
      }
    } catch (error) {
      console.error('Resend email OTP error:', error)
      errorHandler.handleApiError(error, {
        context: "email_verification",
        fallbackMessage: "Failed to resend email verification code. Please try again.",
      })
    }
  }

  // Handle resend phone OTP
  const handleResendPhoneOTP = async () => {
    try {
      // Get a new reCAPTCHA token
      const token = await getRecaptchaToken();
      
      if (!token) {
        throw new Error('Failed to get reCAPTCHA verification. Please try again.');
      }
      
      // Request new phone OTP with reCAPTCHA token
      const result = await sendPhoneVerificationCode(formData.contactNumber, token);
      
      if (result.success) {
        // Update verification ID 
        setVerificationId(result.verificationId);
        alertManager.showSuccess('Success', 'OTP has been resent to your phone');
      } else {
        throw new Error(result.error || 'Failed to resend phone OTP');
      }
    } catch (error) {
      console.error('Resend phone OTP error:', error);
      errorHandler.handleApiError(error, {
        context: "phone_verification",
        fallbackMessage: "Failed to resend phone verification code. Please try again.",
      });
    }
  }

  // Render the main screen
  return (
    <View style={styles.container}>
      <StatusBarManager />
      <Header title={
        currentStep === STEPS.FORM
          ? "Register"
          : currentStep === STEPS.EMAIL_OTP
            ? "Email Verification"
            : "Phone Verification"
      } onBackPress={() => {
        if (currentStep === STEPS.FORM) {
          navigation.goBack();
        } else if (currentStep === STEPS.EMAIL_OTP) {
          setCurrentStep(STEPS.FORM);
        } else if (currentStep === STEPS.PHONE_OTP) {
          setCurrentStep(STEPS.EMAIL_OTP);
        }
      }} />

      {renderCurrentStep()}
      
      {/* Invisible reCAPTCHA component for Firebase Phone Auth */}
      <FirebaseRecaptcha
        onTokenReceived={(token) => {
          console.log('reCAPTCHA token received:', token);
        }}
      />
    </View>
  )
}

// Styles for all components
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingBottom: spacing.large,
  },
  formContainer: {
    padding: spacing.medium,
  },
  sectionTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
  },
  button: {
    marginTop: spacing.large,
  },
  otpContainer: {
    flex: 1,
    padding: spacing.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verificationTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.small,
    textAlign: 'center',
  },
  verificationText: {
    ...textStyles.body1,
    color: colors.textMedium,
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.medium,
    marginBottom: spacing.large,
  },
  resendText: {
    ...textStyles.body1,
    color: colors.textMedium,
    marginRight: spacing.small,
  },
  resendButton: {
    ...textStyles.body1,
    fontWeight: 'bold',
    color: colors.primary,
  },
  disabledText: {
    color: colors.textLight,
  },
})

export default RegisterScreen

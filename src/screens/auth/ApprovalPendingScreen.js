import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image,
  BackHandler,
  Alert
} from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Button from '../../components/common/Button';
import StatusBarManager from '../../components/common/StatusBarManager';
import { useAuth } from '../../store/auth/AuthContext';
import { useFocusEffect } from '@react-navigation/native';

/**
 * Approval Pending screen shown to users who are logged in but not yet approved
 */
const ApprovalPendingScreen = () => {
  const { logout, user } = useAuth();

  // Handle back button press to prevent going back to login
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        Alert.alert(
          'Exit App',
          'Do you want to log out and exit the app?',
          [
            { text: 'Cancel', style: 'cancel', onPress: () => {} },
            { text: 'Log Out', style: 'destructive', onPress: handleLogout }
          ],
          { cancelable: false }
        );
        return true; // Prevent default behavior
      };

      // Correct way to add and remove back handler in newer RN versions
      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      
      return () => subscription.remove(); // This is the proper cleanup
    }, [])
  );

  // Use logout directly when user wants to logout
  const handleLogout = async () => {
    try {
      // Clear local auth state first - don't need to navigate, AuthContext will handle it
      await logout();
      // Navigation will happen automatically from AppNavigator based on auth state
    } catch (error) {
      console.error('Logout error:', error);
      // Even if there's an error, we still want to try to complete the logout
      // No need to use navigation here as AppNavigator will handle it
    }
  };
  
  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />
      
      <View style={styles.content}>
        <Image
          source={require('../../assets/images/hourglass.png')}
          style={styles.image}
          resizeMode="contain"
        />
        
        <Text style={styles.title}>Account Pending Approval</Text>
        <Text style={styles.message}>
          Your account is under review. Please allow 1-2 business days for approval.
        </Text>
        
        <Text style={styles.companyInfo}>
          Company: {user?.companyName || 'Your Company'}
        </Text>
        <Text style={styles.companyInfo}>
          Email: {user?.email || '<EMAIL>'}
        </Text>
        
        <Button
          title="Log Out"
          onPress={handleLogout}
          style={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.large,
  },
  image: {
    width: 120,
    height: 120,
    marginBottom: spacing.large,
  },
  title: {
    ...textStyles.heading2,
    color: colors.primary,
    marginBottom: spacing.medium,
    textAlign: 'center',
  },
  message: {
    ...textStyles.body,
    color: colors.textDark,
    textAlign: 'center',
    marginBottom: spacing.extraLarge,
  },
  companyInfo: {
    ...textStyles.bodyBold,
    color: colors.textDark,
    marginBottom: spacing.small,
  },
  button: {
    marginTop: spacing.extraLarge,
    width: '80%',
  },
});

export default ApprovalPendingScreen;

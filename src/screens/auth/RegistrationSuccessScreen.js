import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  Image
} from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Header from '../../components/common/Header';
import Button from '../../components/common/Button';
import StatusBarManager from '../../components/common/StatusBarManager';

/**
 * Registration success screen shown after successful registration
 */
const RegistrationSuccessScreen = ({ navigation }) => {
  // Navigate to login screen
  const handleGoToLogin = () => {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Login' }],
    });
  };
  
  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />
      <Header 
        title="" 
        onBackPress={() => navigation.goBack()} 
      />
      
      <View style={styles.content}>
        <Image
          source={require('../../assets/images/hourglass.png')}
          style={styles.image}
          resizeMode="contain"
        />
        
        <Text style={styles.title}>Thank you for registering!</Text>
        <Text style={styles.message}>
          Your account is under review. Please allow 1-2 business days for approval
        </Text>
        
        <Button
          title="Go to Login"
          onPress={handleGoToLogin}
          style={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.primary,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: spacing.large,
  },
  image: {
    width: 120,
    height: 120,
    marginBottom: spacing.large,
  },
  title: {
    ...textStyles.heading2,
    color: colors.white,
    textAlign: 'center',
    marginBottom: spacing.medium,
  },
  message: {
    ...textStyles.body1,
    color: colors.white,
    textAlign: 'center',
    marginBottom: spacing.xlarge,
  },
  button: {
    width: '100%',
    backgroundColor: colors.white,
  },
});

export default RegistrationSuccessScreen;

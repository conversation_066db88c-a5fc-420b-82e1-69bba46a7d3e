import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Header from '../../components/common/Header';
import Button from '../../components/common/Button';
import OTPInput from '../../components/common/OTPInput';
import StatusBarManager from '../../components/common/StatusBarManager';
import { useAuth } from '../../store/auth/AuthContext';
import errorHandler from '../../utils/errorHandler';
import alertManager from '../../utils/alertManager';
import { sendPhoneVerificationCode, verifyPhoneNumber } from '../../utils/firebase';

/**
 * OTP Verification screen for authentication and registration
 */
const OTPVerificationScreen = ({ navigation, route }) => {
  const {
    verificationType = 'email',
    email,
    phoneNumber,
    verificationId,
    isRegistration = false,
    isLogin = false,
    message
  } = route.params || {};

  const {
    verifyEmailOtp,
    verifyPhoneOtp,
    requestEmailOtp,
    requestPhoneOtp,
    verificationData,
    registrationData,
    loginWithPhoneOtp
  } = useAuth();

  const [otp, setOtp] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [currentVerificationId, setCurrentVerificationId] = useState(verificationId);

  const timerRef = useRef(null);

  // Start countdown timer on mount
  useEffect(() => {
    startCountdown();

    // Clean up timer on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  /**
   * Start countdown for OTP resend
   */
  const startCountdown = () => {
    setCanResend(false);
    setCountdown(60);

    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          clearInterval(timerRef.current);
          setCanResend(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  /**
   * Format countdown time in MM:SS format
   * @param {number} seconds - Total seconds remaining
   * @returns {string} - Formatted time string
   */
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  /**
   * Handle OTP verification submission
   */
  const handleVerify = async () => {
    if (otp.length !== 6) {
      alertManager.showError('Error', 'Please enter the complete OTP');
      return;
    }

    setIsLoading(true);
    try {
      // Handle verification based on type
      if (verificationType === 'email') {
        const response = await verifyEmailOtp(otp);

        // Check if this is part of registration flow
        if (isRegistration && response.data?.nextStep === 'phone_verification') {
          // Email verified, now proceed to phone verification
          // Get the phone number from registration data
          const phoneNumber = registrationData?.contactNumber;
          if (!phoneNumber) {
            throw new Error('Phone number not found in registration data');
          }

          // Format phone number with country code if not present
          const formattedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+91${phoneNumber}`;

          // Request phone OTP
          const phoneResponse = await requestPhoneOtp(formattedPhone);

          // Update verification type and continue on same screen
          navigation.setParams({
            verificationType: 'phone',
            phoneNumber: formattedPhone,
            isRegistration: true,
            message: 'Please verify your phone number to complete registration.',
            verificationId: phoneResponse?.data?.verificationId || null
          });

          // Reset OTP input
          setOtp('');

          // Reset countdown
          startCountdown();

          // Show success message with custom buttons to prevent navigation issues
          alertManager.showSuccess(
            'Email Verified',
            'Your email has been verified successfully. Now please verify your phone number to complete registration.',
            [{
              text: 'OK',
              onPress: () => {
                // Do nothing, let the user stay on the same screen
                console.log('User acknowledged email verification');
              }
            }]
          );
        } else if (isLogin) {
          // Email login verification
          navigation.reset({
            index: 0,
            routes: [{ name: 'Main' }],
          });
        } else {
          // Normal email verification (not registration or login)
          navigation.reset({
            index: 0,
            routes: [{ name: 'Main' }],
          });
        }
      } else if (verificationType === 'phone') {
        // Verify phone OTP
        if (!currentVerificationId && (!verificationData || verificationData.type !== 'phone')) {
          alertManager.showError('Error', 'Missing verification ID. Please request a new OTP.', [{
            text: 'OK',
            onPress: () => {
              console.log('User acknowledged missing verification ID');
            }
          }]);
          return;
        }

        // Check if this is a login attempt
        if (isLogin) {
          // This is a phone login attempt
          const response = await loginWithPhoneOtp(phoneNumber, currentVerificationId || verificationId, otp);

          if (response.status === 'success') {
            // Check if user is approved
            if (response.data && response.data.rider && response.data.rider.isApproved) {
              // User is approved, navigate to main app
              navigation.reset({
                index: 0,
                routes: [{ name: 'Main' }],
              });
            } else {
              // User is not approved, navigate to approval pending screen
              navigation.reset({
                index: 0,
                routes: [{ name: 'ApprovalPending' }],
              });
            }
          } else {
            alertManager.showError('Login Failed', response.message || 'An error occurred during login', [{
              text: 'OK',
              onPress: () => {
                console.log('User acknowledged login failure');
              }
            }]);
          }
        } else if (isRegistration) {
          // This is part of registration flow
          const response = await verifyPhoneOtp(otp);

          // Both email and phone are now verified, registration complete
          navigation.navigate('RegistrationSuccess');
        } else {
          // Normal phone verification (not registration or login)
          const response = await verifyPhoneOtp(otp);

          navigation.reset({
            index: 0,
            routes: [{ name: 'Main' }],
          });
        }
      } else {
        throw new Error('Unknown verification type');
      }
    } catch (error) {
      console.error(`OTP verification error (${verificationType}):`, error);

      if (typeof errorHandler.handleApiError === 'function') {
        errorHandler.handleApiError(error, {
          context: `otp_verification_${verificationType}`,
          fallbackMessage: 'Invalid OTP. Please try again.'
        });
      } else {
        // Fallback if errorHandler.handleApiError is not a function
        console.error('Error handler is not a function:', error);
        Alert.alert(
          'Verification Failed',
          error.response?.data?.message || error.message || 'Invalid OTP. Please try again.'
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle OTP resend request
   */
  const handleResend = async () => {
    if (!canResend) return;

    setIsLoading(true);
    try {
      if (verificationType === 'email' || verificationType === 'registration') {
        // Use callback approach with showAlert=true since we're already on the OTP screen
        await requestEmailOtp(
          email,
          null,
          true, // Show alert for resend
          () => {
            // Start countdown after OTP is sent
            startCountdown();
            // Show success message
            alertManager.showSuccess('Success', 'OTP has been resent successfully', [
              {
                text: 'OK',
                onPress: () => {
                  console.log('User acknowledged OTP resend');
                }
              }
            ]);
          }
        );
      } else if (verificationType === 'phone') {
        const response = await requestPhoneOtp(phoneNumber);
        // Update verification ID if available in the response
        if (response && response.data && response.data.verificationId) {
          setCurrentVerificationId(response.data.verificationId);
        }
        startCountdown();
      }
    } catch (error) {
      if (typeof errorHandler.handleApiError === 'function') {
        errorHandler.handleApiError(error, {
          context: `otp_resend_${verificationType}`,
          fallbackMessage: 'Failed to resend OTP. Please try again.'
        });
      } else {
        // Fallback if errorHandler.handleApiError is not a function
        console.error('Error handler is not a function:', error);
        alertManager.showError(
          'Resend Failed',
          error.response?.data?.message || error.message || 'Failed to resend OTP. Please try again.'
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Determine verification context text
  const getVerificationContext = () => {
    // If a custom message is provided, use it
    if (message) {
      return message;
    }

    // Otherwise, use default messages based on verification type
    switch(verificationType) {
      case 'email':
        return `We've sent a verification code to ${email}`;
      case 'phone':
        return `We've sent a verification code to ${phoneNumber}`;
      default:
        return 'Enter the verification code';
    }
  };

  return (
    <View style={styles.container}>
      <StatusBarManager />
      <Header
        title="OTP Verification"
        onBackPress={() => navigation.goBack()}
      />

      <View style={styles.content}>
        <Text style={styles.verificationText}>{getVerificationContext()}</Text>

        <OTPInput
          length={6}
          onCodeFilled={setOtp}
          autoFocus
        />

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>
            Resend Code in: {formatTime(countdown)}
          </Text>

          <TouchableOpacity
            onPress={handleResend}
            disabled={!canResend || isLoading}
          >
            <Text style={[
              styles.resendButton,
              (!canResend || isLoading) && styles.disabledText
            ]}>
              Resend Code
            </Text>
          </TouchableOpacity>
        </View>

        <Button
          title="Verify"
          onPress={handleVerify}
          loading={isLoading}
          style={styles.button}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  content: {
    flex: 1,
    padding: spacing.medium,
    alignItems: 'center',
  },
  verificationText: {
    ...textStyles.body1,
    color: colors.textDark,
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginTop: spacing.large,
  },
  resendText: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  resendButton: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: 'bold',
  },
  disabledText: {
    color: colors.gray,
  },
  button: {
    width: '100%',
    marginTop: 'auto',
    marginBottom: spacing.large,
  },
});

export default OTPVerificationScreen;

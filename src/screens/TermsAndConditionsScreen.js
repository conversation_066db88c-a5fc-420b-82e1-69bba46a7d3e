"use client"

import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';

const TermsAndConditionsScreen = () => {
  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Terms & Conditions</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.lastUpdated}>Last Updated: May 12, 2025</Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>1. Acceptance of Terms</Text>
          <Text style={styles.paragraph}>
            By accessing or using the KraftApp mobile application ("App"), you agree to be bound by these Terms and Conditions. If you do not agree to these terms, please do not use the App.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>2. User Accounts</Text>
          <Text style={styles.paragraph}>
            2.1. To use certain features of the App, you must register for an account. You agree to provide accurate, current, and complete information during the registration process.
          </Text>
          <Text style={styles.paragraph}>
            2.2. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.
          </Text>
          <Text style={styles.paragraph}>
            2.3. You agree to notify us immediately of any unauthorized use of your account or any other breach of security.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>3. Ordering and Payments</Text>
          <Text style={styles.paragraph}>
            3.1. All orders placed through the App are subject to acceptance and availability.
          </Text>
          <Text style={styles.paragraph}>
            3.2. Prices for products are as displayed on the App and are subject to change without notice.
          </Text>
          <Text style={styles.paragraph}>
            3.3. Payment must be made in full at the time of ordering. We accept various payment methods as specified in the App.
          </Text>
          <Text style={styles.paragraph}>
            3.4. By placing an order, you represent and warrant that the payment information you provide is valid and that you are authorized to use the payment method.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>4. Shipping and Delivery</Text>
          <Text style={styles.paragraph}>
            4.1. Delivery times are estimates only and are not guaranteed. We are not responsible for delays beyond our control.
          </Text>
          <Text style={styles.paragraph}>
            4.2. Risk of loss and title for items purchased pass to you upon delivery of the items to the shipping carrier.
          </Text>
          <Text style={styles.paragraph}>
            4.3. You are responsible for providing accurate shipping information. We are not liable for delivery issues arising from incorrect information.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>5. Returns and Refunds</Text>
          <Text style={styles.paragraph}>
            5.1. Returns are accepted within 7 days of delivery, provided the product is in its original condition.
          </Text>
          <Text style={styles.paragraph}>
            5.2. Custom orders cannot be returned unless there is a manufacturing defect.
          </Text>
          <Text style={styles.paragraph}>
            5.3. Refunds will be processed within 14 business days of receiving the returned product.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>6. Intellectual Property</Text>
          <Text style={styles.paragraph}>
            6.1. All content, design, graphics, compilation, and other matters related to the App are protected by copyright, trademark, and other intellectual property rights.
          </Text>
          <Text style={styles.paragraph}>
            6.2. You may not reproduce, distribute, modify, create derivative works of, publicly display, or exploit any content from the App without our prior written consent.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>7. Limitation of Liability</Text>
          <Text style={styles.paragraph}>
            7.1. To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, consequential, or punitive damages arising out of or relating to your use of the App.
          </Text>
          <Text style={styles.paragraph}>
            7.2. Our total liability for any claims arising under these Terms shall not exceed the amount paid by you for the product or service that is the subject of the claim.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>8. Governing Law</Text>
          <Text style={styles.paragraph}>
            These Terms shall be governed by and construed in accordance with the laws of India, without regard to its conflict of law provisions.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>9. Changes to Terms</Text>
          <Text style={styles.paragraph}>
            We reserve the right to modify these Terms at any time. Changes will be effective immediately upon posting on the App. Your continued use of the App after any changes constitutes your acceptance of the new Terms.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>10. Contact Information</Text>
          <Text style={styles.paragraph}>
            If you have any questions about these Terms, please contact <NAME_EMAIL>.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: 'center',
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  lastUpdated: {
    ...textStyles.body2,
    color: colors.textLight,
    fontStyle: 'italic',
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  section: {
    marginBottom: spacing.large,
  },
  sectionTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.small,
    fontWeight: '600',
  },
  paragraph: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.medium,
    lineHeight: 22,
  },
});

export default TermsAndConditionsScreen;

"use client"

import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, Alert } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Button from '../components/common/Button';
import errorHandler from '../utils/errorHandler';

const ContactSupportScreen = ({ navigation }) => {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // Handle submit support request
  const handleSubmit = async () => {
    // Validate form
    if (!subject.trim()) {
      Alert.alert('Error', 'Please enter a subject');
      return;
    }

    if (!message.trim()) {
      Alert.alert('Error', 'Please enter your message');
      return;
    }

    setSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Reset form
      setSubject('');
      setMessage('');

      // Show success message
      Alert.alert(
        'Request Submitted', 
        'Your support request has been submitted successfully. We\'ll get back to you soon.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      errorHandler.handleApiError(error, {
        context: 'submit_support',
        fallbackMessage: 'Failed to submit support request. Please try again.',
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Contact Support</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.description}>
          Need help? Fill out the form below and our support team will get back to you as soon as possible.
        </Text>

        <View style={styles.formContainer}>
          <Text style={styles.label}>Subject</Text>
          <TextInput
            style={styles.input}
            value={subject}
            onChangeText={setSubject}
            placeholder="Enter subject"
          />

          <Text style={styles.label}>Message</Text>
          <TextInput
            style={styles.messageInput}
            value={message}
            onChangeText={setMessage}
            placeholder="Enter your message here"
            multiline
            numberOfLines={6}
            textAlignVertical="top"
          />

          <Button
            title="Submit Request"
            onPress={handleSubmit}
            loading={submitting}
            style={styles.submitButton}
          />
        </View>

        <View style={styles.contactInfo}>
          <Text style={styles.contactTitle}>Other Ways to Reach Us</Text>
          
          <View style={styles.contactItem}>
            <Text style={styles.contactLabel}>Email:</Text>
            <Text style={styles.contactValue}><EMAIL></Text>
          </View>
          
          <View style={styles.contactItem}>
            <Text style={styles.contactLabel}>Phone:</Text>
            <Text style={styles.contactValue}>+91 1234567890</Text>
          </View>
          
          <View style={styles.contactItem}>
            <Text style={styles.contactLabel}>Hours:</Text>
            <Text style={styles.contactValue}>Monday - Friday, 9:00 AM - 6:00 PM IST</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: 'center',
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  description: {
    ...textStyles.body1,
    color: colors.textDark,
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  formContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.large,
    elevation: 2,
  },
  label: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.small,
    fontWeight: '500',
  },
  input: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.medium,
    ...textStyles.body1,
  },
  messageInput: {
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.medium,
    ...textStyles.body1,
    minHeight: 120,
  },
  submitButton: {
    marginTop: spacing.medium,
  },
  contactInfo: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    elevation: 2,
  },
  contactTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
    textAlign: 'center',
  },
  contactItem: {
    marginBottom: spacing.medium,
  },
  contactLabel: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.tiny,
  },
  contactValue: {
    ...textStyles.body1,
    color: colors.textDark,
  },
});

export default ContactSupportScreen;

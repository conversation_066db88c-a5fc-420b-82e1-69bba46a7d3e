"use client"

import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';

const PrivacyPolicyScreen = () => {
  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Privacy Policy</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.lastUpdated}>Last Updated: May 12, 2025</Text>

        <Text style={styles.introduction}>
          KraftApp ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application.
        </Text>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>1. Information We Collect</Text>
          <Text style={styles.paragraph}>
            We may collect the following types of information:
          </Text>
          <Text style={styles.bulletPoint}>• Personal Information: Name, email address, phone number, shipping address, and payment information.</Text>
          <Text style={styles.bulletPoint}>• Business Information: Company name, GST number, and business address.</Text>
          <Text style={styles.bulletPoint}>• Device Information: Device type, operating system, and unique device identifiers.</Text>
          <Text style={styles.bulletPoint}>• Usage Information: How you interact with our app, including pages visited and features used.</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>2. How We Use Your Information</Text>
          <Text style={styles.paragraph}>
            We may use the information we collect for various purposes, including:
          </Text>
          <Text style={styles.bulletPoint}>• Processing and fulfilling your orders</Text>
          <Text style={styles.bulletPoint}>• Creating and managing your account</Text>
          <Text style={styles.bulletPoint}>• Providing customer support</Text>
          <Text style={styles.bulletPoint}>• Sending transactional emails and order updates</Text>
          <Text style={styles.bulletPoint}>• Improving our app and services</Text>
          <Text style={styles.bulletPoint}>• Sending marketing communications (with your consent)</Text>
          <Text style={styles.bulletPoint}>• Complying with legal obligations</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>3. Sharing Your Information</Text>
          <Text style={styles.paragraph}>
            We may share your information with:
          </Text>
          <Text style={styles.bulletPoint}>• Service providers who help us operate our business</Text>
          <Text style={styles.bulletPoint}>• Payment processors to complete transactions</Text>
          <Text style={styles.bulletPoint}>• Shipping partners to deliver your orders</Text>
          <Text style={styles.bulletPoint}>• Legal authorities when required by law</Text>
          <Text style={styles.paragraph}>
            We do not sell your personal information to third parties.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>4. Data Security</Text>
          <Text style={styles.paragraph}>
            We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, accidental loss, or destruction. However, no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee absolute security.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>5. Your Rights</Text>
          <Text style={styles.paragraph}>
            Depending on your location, you may have certain rights regarding your personal information, including:
          </Text>
          <Text style={styles.bulletPoint}>• Access to your personal information</Text>
          <Text style={styles.bulletPoint}>• Correction of inaccurate information</Text>
          <Text style={styles.bulletPoint}>• Deletion of your information</Text>
          <Text style={styles.bulletPoint}>• Restriction of processing</Text>
          <Text style={styles.bulletPoint}>• Data portability</Text>
          <Text style={styles.bulletPoint}>• Objection to processing</Text>
          <Text style={styles.paragraph}>
            To exercise these rights, please contact us using the information provided at the end of this policy.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>6. Retention of Information</Text>
          <Text style={styles.paragraph}>
            We retain your personal information for as long as necessary to fulfill the purposes outlined in this Privacy Policy, unless a longer retention period is required or permitted by law.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>7. Children's Privacy</Text>
          <Text style={styles.paragraph}>
            Our app is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If you are a parent or guardian and believe your child has provided us with personal information, please contact us.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>8. Changes to This Privacy Policy</Text>
          <Text style={styles.paragraph}>
            We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date. You are advised to review this Privacy Policy periodically for any changes.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>9. Contact Us</Text>
          <Text style={styles.paragraph}>
            If you have any questions or concerns about this Privacy Policy, please contact us at:
          </Text>
          <Text style={styles.contactInfo}>Email: <EMAIL></Text>
          <Text style={styles.contactInfo}>Address: 123 Paper Street, Kraft City, India 400001</Text>
          <Text style={styles.contactInfo}>Phone: +91 1234567890</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: 'center',
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  lastUpdated: {
    ...textStyles.body2,
    color: colors.textLight,
    fontStyle: 'italic',
    marginBottom: spacing.medium,
    textAlign: 'center',
  },
  introduction: {
    ...textStyles.body1,
    color: colors.textDark,
    marginBottom: spacing.large,
    lineHeight: 24,
  },
  section: {
    marginBottom: spacing.large,
  },
  sectionTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.small,
    fontWeight: '600',
  },
  paragraph: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.medium,
    lineHeight: 22,
  },
  bulletPoint: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.small,
    marginLeft: spacing.medium,
    lineHeight: 22,
  },
  contactInfo: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.small,
    fontWeight: '500',
  },
});

export default PrivacyPolicyScreen;

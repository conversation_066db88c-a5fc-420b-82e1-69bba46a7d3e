"use client"

import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Button from '../components/common/Button';
import Icon from 'react-native-vector-icons/Feather';

/**
 * Success screen to show after order submission or registration
 * @param {object} props
 * @param {object} props.route - Route params
 * @param {string} props.route.params.type - Type of success ('order' or 'registration')
 * @param {string} props.route.params.title - Title to display
 * @param {string} props.route.params.message - Message to display
 * @param {string} props.route.params.buttonText - Text for primary button
 * @param {string} props.route.params.buttonDestination - Destination screen for primary button
 * @param {object} props.navigation - Navigation object
 */
const SuccessScreen = ({ route, navigation }) => {
  const {
    type = 'order',
    title = type === 'order' ? 'Order Submitted for Approval' : 'Registration Submitted',
    message = type === 'order'
      ? 'Your order has been submitted for approval. We will notify you once it has been processed.'
      : 'Your registration has been submitted. We will notify you once your account has been approved.',
    buttonText = type === 'order' ? 'View Orders' : 'Back to Login',
    buttonDestination = type === 'order' ? 'OrdersTab' : 'Login',
  } = route.params || {};

  // Handle primary button press
  const handlePrimaryAction = () => {
    if (type === 'order') {
      // For order success, navigate to Orders tab in the main navigator
      navigation.reset({
        index: 0,
        routes: [
          { 
            name: 'Tabs', 
            state: {
              routes: [{ name: buttonDestination }]
            }
          }
        ],
      });
    } else {
      // For registration success, navigate to Login
      navigation.navigate(buttonDestination);
    }
  };

  // Handle secondary button press (go to home)
  const handleGoHome = () => {
    if (type === 'order') {
      // Reset navigation to home tab
      navigation.reset({
        index: 0,
        routes: [
          { 
            name: 'Tabs', 
            state: {
              routes: [{ name: 'HomeTab' }]
            }
          }
        ],
      });
    }
  };

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.content}>
        <View style={styles.iconContainer}>
          <Icon name="check-circle" size={80} color={colors.success} />
        </View>

        <Text style={styles.title}>{title}</Text>
        <Text style={styles.message}>{message}</Text>

        <View style={styles.buttonContainer}>
          <Button
            title={buttonText}
            onPress={handlePrimaryAction}
            style={styles.button}
          />

          {type === 'order' && (
            <Button
              title="Continue Shopping"
              variant="outline"
              onPress={handleGoHome}
              style={styles.button}
            />
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing.large,
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.lightGray || '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.large,
  },
  title: {
    ...textStyles.heading2,
    color: colors.textDark,
    textAlign: 'center',
    marginBottom: spacing.medium,
  },
  message: {
    ...textStyles.body1,
    color: colors.textLight,
    textAlign: 'center',
    marginBottom: spacing.xlarge,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 300,
  },
  button: {
    marginBottom: spacing.medium,
  },
});

export default SuccessScreen;

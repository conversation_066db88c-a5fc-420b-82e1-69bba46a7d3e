"use client"

import { useState, useEffect } from "react"
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  FlatList,
} from "react-native"
import { colors } from "../theme/colors"
import { spacing } from "../theme/spacing"
import { textStyles } from "../theme/typography"
import StatusBarManager from "../components/common/StatusBarManager"
import Button from "../components/common/Button"
import { enquiryApi } from "../api"
import errorHandler from "../utils/errorHandler"

const EnquiriesScreen = () => {
  const [subject, setSubject] = useState("")
  const [message, setMessage] = useState("")
  const [subjectError, setSubjectError] = useState("")
  const [messageError, setMessageError] = useState("")
  const [submitting, setSubmitting] = useState(false)
  const [enquiries, setEnquiries] = useState([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(true)

  // Fetch enquiries on component mount
  useEffect(() => {
    fetchEnquiries()
  }, [])

  // Fetch enquiries from API
  const fetchEnquiries = async () => {
    setLoading(true)
    try {
      const response = await enquiryApi.getEnquiries()
      console.log('Enquiries response:', response)

      // Handle different response formats
      if (response?.enquiries && Array.isArray(response.enquiries)) {
        setEnquiries(response.enquiries)
      } else if (response?.data?.enquiries && Array.isArray(response.data.enquiries)) {
        setEnquiries(response.data.enquiries)
      } else if (Array.isArray(response)) {
        setEnquiries(response)
      } else {
        // If we can't find enquiries in the expected format, set empty array
        console.warn('Unexpected enquiries response format:', response)
        setEnquiries([])
      }
    } catch (error) {
      console.error('Error fetching enquiries:', error)
      errorHandler.handleApiError(error, {
        context: "fetch_enquiries",
        fallbackMessage: "Failed to load enquiries. Please try again.",
      })
      setEnquiries([]) // Set empty array on error
    } finally {
      setLoading(false)
    }
  }

  // Validate form fields
  const validateForm = () => {
    let isValid = true

    // Reset error messages
    setSubjectError("")
    setMessageError("")

    // Validate subject
    if (!subject.trim()) {
      setSubjectError("Subject is required")
      isValid = false
    } else if (subject.trim().length < 5) {
      setSubjectError("Subject must be at least 5 characters long")
      isValid = false
    } else if (subject.trim().length > 100) {
      setSubjectError("Subject cannot exceed 100 characters")
      isValid = false
    }

    // Validate message
    if (!message.trim()) {
      setMessageError("Message is required")
      isValid = false
    } else if (message.trim().length < 10) {
      setMessageError("Message must be at least 10 characters long")
      isValid = false
    } else if (message.trim().length > 1000) {
      setMessageError("Message cannot exceed 1000 characters")
      isValid = false
    }

    return isValid
  }

  // Handle submit enquiry
  const handleSubmitEnquiry = async () => {
    // Validate form
    if (!validateForm()) {
      return
    }

    setSubmitting(true)
    try {
      await enquiryApi.submitEnquiry({
        subject: subject.trim(),
        message: message.trim(),
        contactPreference: "EMAIL", // Default to EMAIL since we removed the selection
      })

      // Reset form
      setSubject("")
      setMessage("")
      setSubjectError("")
      setMessageError("")

      // Show success message
      Alert.alert("Enquiry Submitted", "Your enquiry has been submitted successfully. We'll get back to you soon.", [
        {
          text: "OK",
          onPress: () => {
            setShowForm(false)
            fetchEnquiries()
          },
        },
      ])
    } catch (error) {
      // Check for validation errors from the API
      if (error.response?.status === 400 && error.data?.errors) {
        const validationErrors = error.data.errors;

        // Set field-specific errors
        validationErrors.forEach(err => {
          if (err.field === 'subject') {
            setSubjectError(err.message);
          } else if (err.field === 'message') {
            setMessageError(err.message);
          }
        });
      } else {
        errorHandler.handleApiError(error, {
          context: "submit_enquiry",
          fallbackMessage: "Failed to submit enquiry. Please try again.",
        });
      }
    } finally {
      setSubmitting(false)
    }
  }



  // Format date for display
  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch (error) {
      return "Unknown date";
    }
  }

  // Render enquiry item
  const renderEnquiryItem = ({ item }) => (
    <View style={styles.enquiryItem}>
      <View style={styles.enquiryHeader}>
        <Text style={styles.enquirySubject}>{item.subject || "No Subject"}</Text>
        <Text style={styles.enquiryDate}>{formatDate(item.createdAt)}</Text>
      </View>

      <Text style={styles.enquiryMessage}>{item.message || "No message content"}</Text>

      {item.enquiryNumber && (
        <Text style={styles.enquiryNumber}>Ref: {item.enquiryNumber}</Text>
      )}

      <View style={styles.enquiryFooter}>
        <Text
          style={[
            styles.enquiryStatus,
            item.status === "ANSWERED" || item.status === "RESOLVED"
              ? styles.answeredStatus
              : styles.pendingStatus
          ]}
        >
          {item.status || "PENDING"}
        </Text>

        {item.response && (
          <TouchableOpacity
            style={styles.viewResponseButton}
            onPress={() => Alert.alert("Response", item.response)}
          >
            <Text style={styles.viewResponseText}>View Response</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Enquiries</Text>
      </View>

      <View style={styles.content}>
        <View style={styles.tabContainer}>
          <TouchableOpacity style={[styles.tab, showForm && styles.activeTab]} onPress={() => setShowForm(true)}>
            <Text style={[styles.tabText, showForm && styles.activeTabText]}>New Enquiry</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.tab, !showForm && styles.activeTab]} onPress={() => setShowForm(false)}>
            <Text style={[styles.tabText, !showForm && styles.activeTabText]}>My Enquiries</Text>
          </TouchableOpacity>
        </View>

        {showForm ? (
          <ScrollView style={styles.formContainer}>
            <Text style={styles.label}>Subject</Text>
            <TextInput
              style={[styles.input, subjectError ? styles.inputError : null]}
              value={subject}
              onChangeText={(text) => {
                setSubject(text)
                if (subjectError) setSubjectError("")
              }}
              placeholder="Enter subject (min 5 characters)"
            />
            {subjectError ? <Text style={styles.errorText}>{subjectError}</Text> : null}

            <Text style={styles.label}>Message</Text>
            <TextInput
              style={[styles.messageInput, messageError ? styles.inputError : null]}
              value={message}
              onChangeText={(text) => {
                setMessage(text)
                if (messageError) setMessageError("")
              }}
              placeholder="Enter your message here (min 10 characters)"
              multiline
              numberOfLines={6}
              textAlignVertical="top"
            />
            {messageError ? <Text style={styles.errorText}>{messageError}</Text> : null}

            <Text style={styles.noteText}>
              Note: Your enquiry will be responded to via your registered email address.
            </Text>

            <Button
              title="Submit Enquiry"
              onPress={handleSubmitEnquiry}
              loading={submitting}
              style={styles.submitButton}
            />
          </ScrollView>
        ) : (
          <>
            {loading ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Loading enquiries...</Text>
              </View>
            ) : enquiries.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>You haven't submitted any enquiries yet</Text>
                <Button
                  title="Submit New Enquiry"
                  variant="outline"
                  onPress={() => setShowForm(true)}
                  style={styles.newEnquiryButton}
                />
              </View>
            ) : (
              <FlatList
                data={enquiries}
                renderItem={renderEnquiryItem}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
              />
            )}
          </>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: "center",
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: "bold",
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.medium,
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: colors.white,
    borderRadius: 8,
    marginBottom: spacing.medium,
    overflow: "hidden",
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: spacing.medium,
    alignItems: "center",
  },
  activeTab: {
    backgroundColor: colors.primary,
  },
  tabText: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "500",
  },
  activeTabText: {
    color: colors.white,
  },
  formContainer: {
    flex: 1,
  },
  label: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "500",
    marginBottom: spacing.small,
  },
  input: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.small,
    ...textStyles.body1,
  },
  messageInput: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    paddingHorizontal: spacing.medium,
    paddingVertical: spacing.medium,
    marginBottom: spacing.small,
    ...textStyles.body1,
    minHeight: 120,
  },
  inputError: {
    borderColor: colors.error || 'red',
  },
  errorText: {
    ...textStyles.caption,
    color: colors.error || 'red',
    marginBottom: spacing.medium,
    marginTop: -spacing.small,
  },
  noteText: {
    ...textStyles.body2,
    color: colors.textLight,
    fontStyle: 'italic',
    marginBottom: spacing.medium,
  },
  enquiryNumber: {
    ...textStyles.caption,
    color: colors.textLight,
    marginBottom: spacing.small,
  },
  submitButton: {
    marginBottom: spacing.large,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginTop: spacing.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginBottom: spacing.medium,
  },
  newEnquiryButton: {
    width: 200,
  },
  listContainer: {
    paddingBottom: spacing.large,
  },
  enquiryItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    padding: spacing.medium,
    marginBottom: spacing.medium,
    elevation: 2,
  },
  enquiryHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.small,
  },
  enquirySubject: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "600",
    flex: 1,
  },
  enquiryDate: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  enquiryMessage: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.medium,
  },
  enquiryFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  enquiryStatus: {
    ...textStyles.caption,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    borderRadius: 4,
    overflow: "hidden",
  },
  pendingStatus: {
    backgroundColor: colors.warning,
    color: colors.white,
  },
  answeredStatus: {
    backgroundColor: colors.success,
    color: colors.white,
  },
  viewResponseButton: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
  },
  viewResponseText: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: "500",
  },
})

export default EnquiriesScreen

"use client"

import { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  TouchableOpacity, 
  Modal, 
  Alert,
  ActivityIndicator
} from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import Icon from 'react-native-vector-icons/Feather';
import errorHandler from '../utils/errorHandler';

const AddressItem = ({ address, onEdit, onDelete, onSetDefault, isDefault }) => {
  return (
    <View style={styles.addressItem}>
      <View style={styles.addressHeader}>
        <Text style={styles.addressName}>{address.name}</Text>
        {isDefault && (
          <View style={styles.defaultBadge}>
            <Text style={styles.defaultText}>Default</Text>
          </View>
        )}
      </View>
      
      <Text style={styles.addressLine}>{address.addressLine1}</Text>
      {address.addressLine2 && <Text style={styles.addressLine}>{address.addressLine2}</Text>}
      <Text style={styles.addressLine}>{address.city}, {address.state} {address.postalCode}</Text>
      <Text style={styles.addressLine}>{address.country}</Text>
      
      <View style={styles.addressActions}>
        {!isDefault && (
          <TouchableOpacity style={styles.addressAction} onPress={() => onSetDefault(address.id)}>
            <Icon name="check-circle" size={16} color={colors.primary} />
            <Text style={styles.actionText}>Set as Default</Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity style={styles.addressAction} onPress={() => onEdit(address)}>
          <Icon name="edit" size={16} color={colors.primary} />
          <Text style={styles.actionText}>Edit</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.addressAction} onPress={() => onDelete(address.id)}>
          <Icon name="trash-2" size={16} color={colors.error} />
          <Text style={[styles.actionText, { color: colors.error }]}>Delete</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const ShippingAddressesScreen = ({ navigation }) => {
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [defaultAddressId, setDefaultAddressId] = useState(null);
  
  // Form state
  const [addressForm, setAddressForm] = useState({
    name: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'India',
  });
  
  // Form errors
  const [errors, setErrors] = useState({});
  
  // Fetch addresses
  const fetchAddresses = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data
      const mockAddresses = [
        {
          id: '1',
          name: 'Office',
          addressLine1: '123 Business Park',
          addressLine2: 'Sector 5',
          city: 'Alwar',
          state: 'Rajasthan',
          postalCode: '321633',
          country: 'India',
        },
        {
          id: '2',
          name: 'Factory',
          addressLine1: 'Plot 45, Industrial Area',
          addressLine2: '',
          city: 'Bhiwadi',
          state: 'Rajasthan',
          postalCode: '301019',
          country: 'India',
        },
      ];
      
      setAddresses(mockAddresses);
      setDefaultAddressId('1'); // Set first address as default
    } catch (error) {
      errorHandler.handleApiError(error, {
        context: 'fetch_addresses',
        fallbackMessage: 'Failed to load addresses. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };
  
  // Load addresses on mount
  useEffect(() => {
    fetchAddresses();
  }, []);
  
  // Handle add new address
  const handleAddAddress = () => {
    setEditingAddress(null);
    setAddressForm({
      name: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'India',
    });
    setErrors({});
    setModalVisible(true);
  };
  
  // Handle edit address
  const handleEditAddress = (address) => {
    setEditingAddress(address);
    setAddressForm({
      name: address.name,
      addressLine1: address.addressLine1,
      addressLine2: address.addressLine2 || '',
      city: address.city,
      state: address.state,
      postalCode: address.postalCode,
      country: address.country,
    });
    setErrors({});
    setModalVisible(true);
  };
  
  // Handle delete address
  const handleDeleteAddress = (addressId) => {
    Alert.alert(
      'Delete Address',
      'Are you sure you want to delete this address?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            // Remove address from state
            setAddresses(prev => prev.filter(addr => addr.id !== addressId));
            
            // If default address is deleted, set a new default
            if (addressId === defaultAddressId && addresses.length > 1) {
              const newDefault = addresses.find(addr => addr.id !== addressId);
              if (newDefault) {
                setDefaultAddressId(newDefault.id);
              }
            }
          }
        },
      ]
    );
  };
  
  // Handle set default address
  const handleSetDefaultAddress = (addressId) => {
    setDefaultAddressId(addressId);
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors = {};
    
    if (!addressForm.name.trim()) {
      newErrors.name = 'Address name is required';
    }
    
    if (!addressForm.addressLine1.trim()) {
      newErrors.addressLine1 = 'Address line 1 is required';
    }
    
    if (!addressForm.city.trim()) {
      newErrors.city = 'City is required';
    }
    
    if (!addressForm.state.trim()) {
      newErrors.state = 'State is required';
    }
    
    if (!addressForm.postalCode.trim()) {
      newErrors.postalCode = 'Postal code is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle save address
  const handleSaveAddress = () => {
    if (!validateForm()) {
      return;
    }
    
    if (editingAddress) {
      // Update existing address
      setAddresses(prev => 
        prev.map(addr => 
          addr.id === editingAddress.id 
            ? { ...addr, ...addressForm }
            : addr
        )
      );
    } else {
      // Add new address
      const newAddress = {
        id: Date.now().toString(), // Generate temporary ID
        ...addressForm,
      };
      
      setAddresses(prev => [...prev, newAddress]);
      
      // If this is the first address, set it as default
      if (addresses.length === 0) {
        setDefaultAddressId(newAddress.id);
      }
    }
    
    setModalVisible(false);
  };

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Shipping Addresses</Text>
      </View>

      <View style={styles.content}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading addresses...</Text>
          </View>
        ) : (
          <>
            <FlatList
              data={addresses}
              renderItem={({ item }) => (
                <AddressItem
                  address={item}
                  onEdit={handleEditAddress}
                  onDelete={handleDeleteAddress}
                  onSetDefault={handleSetDefaultAddress}
                  isDefault={item.id === defaultAddressId}
                />
              )}
              keyExtractor={item => item.id}
              contentContainerStyle={styles.listContainer}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Icon name="map-pin" size={64} color={colors.textLight} />
                  <Text style={styles.emptyText}>No addresses found</Text>
                  <Text style={styles.emptySubtext}>Add a shipping address to get started</Text>
                </View>
              }
            />
            
            <Button
              title="Add New Address"
              onPress={handleAddAddress}
              style={styles.addButton}
              leftIcon={<Icon name="plus" size={20} color={colors.white} />}
            />
          </>
        )}
      </View>

      {/* Address Form Modal */}
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>
              {editingAddress ? 'Edit Address' : 'Add New Address'}
            </Text>
            
            <ScrollView>
              <Input
                label="Address Name"
                value={addressForm.name}
                onChangeText={(text) => setAddressForm(prev => ({ ...prev, name: text }))}
                placeholder="e.g. Home, Office, Factory"
                error={errors.name}
              />
              
              <Input
                label="Address Line 1"
                value={addressForm.addressLine1}
                onChangeText={(text) => setAddressForm(prev => ({ ...prev, addressLine1: text }))}
                placeholder="Street address, building name"
                error={errors.addressLine1}
              />
              
              <Input
                label="Address Line 2 (Optional)"
                value={addressForm.addressLine2}
                onChangeText={(text) => setAddressForm(prev => ({ ...prev, addressLine2: text }))}
                placeholder="Apartment, suite, unit, etc."
              />
              
              <Input
                label="City"
                value={addressForm.city}
                onChangeText={(text) => setAddressForm(prev => ({ ...prev, city: text }))}
                placeholder="Enter city"
                error={errors.city}
              />
              
              <View style={styles.formRow}>
                <Input
                  label="State"
                  value={addressForm.state}
                  onChangeText={(text) => setAddressForm(prev => ({ ...prev, state: text }))}
                  placeholder="Enter state"
                  error={errors.state}
                  style={styles.formRowInput}
                />
                
                <Input
                  label="Postal Code"
                  value={addressForm.postalCode}
                  onChangeText={(text) => setAddressForm(prev => ({ ...prev, postalCode: text }))}
                  placeholder="Enter postal code"
                  keyboardType="number-pad"
                  error={errors.postalCode}
                  style={styles.formRowInput}
                />
              </View>
              
              <Input
                label="Country"
                value={addressForm.country}
                onChangeText={(text) => setAddressForm(prev => ({ ...prev, country: text }))}
                placeholder="Enter country"
              />
            </ScrollView>
            
            <View style={styles.modalActions}>
              <Button
                title="Cancel"
                variant="outline"
                onPress={() => setModalVisible(false)}
                style={styles.modalButton}
              />
              <Button
                title="Save"
                onPress={handleSaveAddress}
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: 'center',
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: spacing.medium,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textLight,
    marginTop: spacing.medium,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.xlarge,
  },
  emptyText: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginTop: spacing.medium,
    marginBottom: spacing.small,
  },
  emptySubtext: {
    ...textStyles.body1,
    color: colors.textLight,
    textAlign: 'center',
  },
  listContainer: {
    paddingBottom: 80, // Space for the add button
  },
  addressItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    marginBottom: spacing.medium,
    elevation: 2,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.small,
  },
  addressName: {
    ...textStyles.heading3,
    color: colors.textDark,
  },
  defaultBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny / 2,
    borderRadius: 4,
  },
  defaultText: {
    ...textStyles.caption,
    color: colors.white,
    fontWeight: '500',
  },
  addressLine: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.tiny,
  },
  addressActions: {
    flexDirection: 'row',
    marginTop: spacing.small,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.small,
  },
  addressAction: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: spacing.medium,
  },
  actionText: {
    ...textStyles.body2,
    color: colors.primary,
    marginLeft: spacing.tiny,
  },
  addButton: {
    position: 'absolute',
    bottom: spacing.medium,
    left: spacing.medium,
    right: spacing.medium,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: '90%',
    maxHeight: '80%',
    padding: spacing.medium,
  },
  modalTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
    textAlign: 'center',
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  formRowInput: {
    flex: 1,
    marginHorizontal: spacing.tiny,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.medium,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: spacing.tiny,
  },
});

export default ShippingAddressesScreen;

"use client"

import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { colors } from '../theme/colors';
import { spacing } from '../theme/spacing';
import { textStyles } from '../theme/typography';
import StatusBarManager from '../components/common/StatusBarManager';
import Icon from 'react-native-vector-icons/Feather';

const FAQItem = ({ question, answer }) => {
  const [expanded, setExpanded] = useState(false);

  return (
    <View style={styles.faqItem}>
      <TouchableOpacity 
        style={styles.faqQuestion} 
        onPress={() => setExpanded(!expanded)}
        activeOpacity={0.7}
      >
        <Text style={styles.questionText}>{question}</Text>
        <Icon 
          name={expanded ? 'chevron-up' : 'chevron-down'} 
          size={20} 
          color={colors.textDark} 
        />
      </TouchableOpacity>
      
      {expanded && (
        <View style={styles.faqAnswer}>
          <Text style={styles.answerText}>{answer}</Text>
        </View>
      )}
    </View>
  );
};

const FAQScreen = () => {
  const faqs = [
    {
      question: 'How do I place an order?',
      answer: 'You can place an order by browsing our available stock, adding items to your cart, and proceeding to checkout. Follow the prompts to enter your shipping information and complete your purchase.'
    },
    {
      question: 'What payment methods do you accept?',
      answer: 'We accept various payment methods including credit/debit cards, net banking, UPI, and wallet payments. All transactions are secure and encrypted.'
    },
    {
      question: 'How long does shipping take?',
      answer: 'Shipping typically takes 3-5 business days depending on your location. You can track your order status in the Orders section of your account.'
    },
    {
      question: 'Can I cancel my order?',
      answer: 'Orders can be cancelled within 24 hours of placement if they haven\'t been shipped yet. Please contact our support team for assistance with cancellations.'
    },
    {
      question: 'What is your return policy?',
      answer: 'We accept returns within 7 days of delivery if the product is in its original condition. Please note that custom orders cannot be returned unless there is a manufacturing defect.'
    },
    {
      question: 'How do I track my order?',
      answer: 'You can track your order by going to the Orders section in your account. There you\'ll find real-time updates on your order status and shipping information.'
    },
    {
      question: 'Do you offer bulk discounts?',
      answer: 'Yes, we offer discounts for bulk orders. The discount percentage varies based on the quantity and type of products ordered. Please contact our sales team for more information.'
    },
    {
      question: 'What if my product arrives damaged?',
      answer: 'If your product arrives damaged, please take photos and contact our support team within 48 hours of delivery. We\'ll arrange for a replacement or refund as appropriate.'
    },
    {
      question: 'How do I contact customer support?',
      answer: 'You can contact our customer support team through the Contact Support section in the app, by <NAME_EMAIL>, or by phone at +91 ********** during business hours.'
    },
    {
      question: 'Can I get custom sizes or specifications?',
      answer: 'Yes, we offer custom sizes and specifications for our kraft paper products. Please use the enquiry form to provide your requirements, and our team will get back to you with a quote.'
    }
  ];

  return (
    <View style={styles.container}>
      <StatusBarManager backgroundColor={colors.primary} barStyle="light-content" />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Frequently Asked Questions</Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.description}>
          Find answers to commonly asked questions about our products, ordering process, shipping, and more.
        </Text>

        <View style={styles.faqContainer}>
          {faqs.map((faq, index) => (
            <FAQItem 
              key={index} 
              question={faq.question} 
              answer={faq.answer} 
            />
          ))}
        </View>

        <View style={styles.contactSection}>
          <Text style={styles.contactTitle}>Still have questions?</Text>
          <Text style={styles.contactText}>
            If you couldn't find the answer to your question, please contact our support team.
          </Text>
          <TouchableOpacity style={styles.contactButton}>
            <Text style={styles.contactButtonText}>Contact Support</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    backgroundColor: colors.primary,
    paddingTop: spacing.large,
    paddingBottom: spacing.xlarge,
    paddingHorizontal: spacing.large,
    alignItems: 'center',
  },
  headerTitle: {
    ...textStyles.heading2,
    color: colors.white,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    marginTop: -20,
    backgroundColor: colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  contentContainer: {
    padding: spacing.medium,
    paddingBottom: spacing.xlarge,
  },
  description: {
    ...textStyles.body1,
    color: colors.textDark,
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  faqContainer: {
    marginBottom: spacing.large,
  },
  faqItem: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: spacing.medium,
    overflow: 'hidden',
    elevation: 2,
  },
  faqQuestion: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.medium,
  },
  questionText: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: '500',
    flex: 1,
    marginRight: spacing.small,
  },
  faqAnswer: {
    padding: spacing.medium,
    paddingTop: 0,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  answerText: {
    ...textStyles.body2,
    color: colors.textLight,
    lineHeight: 22,
  },
  contactSection: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    alignItems: 'center',
    elevation: 2,
  },
  contactTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.small,
  },
  contactText: {
    ...textStyles.body1,
    color: colors.textLight,
    textAlign: 'center',
    marginBottom: spacing.medium,
  },
  contactButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.medium,
    paddingHorizontal: spacing.large,
    borderRadius: 8,
  },
  contactButtonText: {
    ...textStyles.body1,
    color: colors.white,
    fontWeight: '500',
  },
});

export default FAQScreen;

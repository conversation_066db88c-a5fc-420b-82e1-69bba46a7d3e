import React, { useState } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Modal, 
  FlatList,
  SafeAreaView
} from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

/**
 * Dropdown component for selection inputs
 * 
 * @param {string} label - Dropdown label
 * @param {array} options - Array of options to display
 * @param {string|object} selectedValue - Currently selected value
 * @param {function} onValueChange - Function called when value changes
 * @param {string} placeholder - Placeholder text when no value is selected
 * @param {object} style - Additional styles for the dropdown
 */
const Dropdown = ({
  label,
  options = [],
  selectedValue,
  onValueChange,
  placeholder = 'Select an option',
  style,
}) => {
  const [modalVisible, setModalVisible] = useState(false);

  // Find the selected option label
  const getSelectedLabel = () => {
    if (!selectedValue) return placeholder;
    
    const selected = options.find(option => 
      option.value === selectedValue || option === selectedValue
    );
    
    return selected ? (selected.label || selected) : placeholder;
  };

  // Handle option selection
  const handleSelect = (option) => {
    onValueChange(option.value || option);
    setModalVisible(false);
  };

  // Render each option in the dropdown
  const renderItem = ({ item }) => (
    <TouchableOpacity
      style={styles.option}
      onPress={() => handleSelect(item)}
    >
      <Text style={[
        styles.optionText,
        (item.value === selectedValue || item === selectedValue) && styles.selectedOptionText
      ]}>
        {item.label || item}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity
        style={styles.dropdownButton}
        onPress={() => setModalVisible(true)}
      >
        <Text style={[
          styles.selectedValueText,
          !selectedValue && styles.placeholderText
        ]}>
          {getSelectedLabel()}
        </Text>
        <Text style={styles.dropdownIcon}>▼</Text>
      </TouchableOpacity>

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text style={styles.modalHeaderText}>{label || 'Select an option'}</Text>
                <TouchableOpacity onPress={() => setModalVisible(false)}>
                  <Text style={styles.closeButton}>✕</Text>
                </TouchableOpacity>
              </View>
              
              <FlatList
                data={options}
                renderItem={renderItem}
                keyExtractor={(item, index) => `option-${index}`}
                style={styles.optionsList}
              />
            </View>
          </SafeAreaView>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing.medium,
  },
  label: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.small,
  },
  dropdownButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.white,
    paddingVertical: spacing.medium,
    paddingHorizontal: spacing.medium,
  },
  selectedValueText: {
    ...textStyles.body1,
    color: colors.text,
    flex: 1,
  },
  placeholderText: {
    color: colors.textLight,
  },
  dropdownIcon: {
    fontSize: 14,
    color: colors.textDark,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: '80%',
  },
  modalContent: {
    padding: spacing.medium,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalHeaderText: {
    ...textStyles.heading3,
    color: colors.text,
  },
  closeButton: {
    fontSize: 20,
    color: colors.textDark,
    padding: spacing.small,
  },
  optionsList: {
    marginTop: spacing.medium,
  },
  option: {
    paddingVertical: spacing.medium,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  optionText: {
    ...textStyles.body1,
    color: colors.text,
  },
  selectedOptionText: {
    color: colors.primary,
    fontWeight: 'bold',
  },
});

export default Dropdown;

import React from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity 
} from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

/**
 * Header component for screens
 * 
 * @param {string} title - Header title
 * @param {function} onBackPress - Function to call when back button is pressed
 * @param {boolean} showBackButton - Whether to show the back button
 * @param {object} style - Additional styles for the header
 */
const Header = ({
  title,
  onBackPress,
  showBackButton = true,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      {showBackButton && (
        <TouchableOpacity
          style={styles.backButton}
          onPress={onBackPress}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
      )}
      
      <Text style={styles.title} numberOfLines={1}>
        {title}
      </Text>
      
      {/* Empty view for layout balance when back button is shown */}
      {showBackButton && <View style={styles.placeholder} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: 56,
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.medium,
  },
  backButton: {
    padding: spacing.small,
  },
  backButtonText: {
    fontSize: 24,
    color: colors.white,
    fontWeight: 'bold',
  },
  title: {
    ...textStyles.heading3,
    color: colors.white,
    flex: 1,
    textAlign: 'center',
  },
  placeholder: {
    width: 40, // Approximately the width of the back button
  },
});

export default Header;

import React, { useRef, useState, useEffect } from 'react';
import <PERSON>Alert from './CustomAlert';
import alertManager from '../../utils/alertManager';

/**
 * Alert provider component to wrap the app and provide global alert functionality
 */
const AlertProvider = ({ children }) => {
  const [alertState, setAlertState] = useState({
    visible: false,
    title: '',
    message: '',
    buttons: [{ text: 'OK', onPress: () => {} }],
    type: 'info',
    dismissable: true,
  });

  const alertRef = useRef({
    setAlertState,
  });

  // Register the alert ref with the alert manager
  useEffect(() => {
    alertManager.setAlertRef(alertRef);
    
    return () => {
      alertManager.setAlertRef(null);
    };
  }, []);

  // Handle alert close
  const handleClose = () => {
    setAlertState(prev => ({
      ...prev,
      visible: false,
    }));
  };

  return (
    <>
      {children}
      <CustomAlert
        visible={alertState.visible}
        title={alertState.title}
        message={alertState.message}
        buttons={alertState.buttons}
        type={alertState.type}
        dismissable={alertState.dismissable}
        onClose={handleClose}
      />
    </>
  );
};

export default AlertProvider;

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Animated,
  Dimensions
} from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';
import Button from './Button';

/**
 * Custom alert component with better styling than the default Alert
 * 
 * @param {boolean} visible - Whether the alert is visible
 * @param {string} title - Alert title
 * @param {string} message - Alert message
 * @param {Array} buttons - Array of button objects with text and onPress
 * @param {function} onClose - Function to call when alert is closed
 * @param {string} type - Alert type ('success', 'error', 'warning', 'info')
 * @param {boolean} dismissable - Whether the alert can be dismissed by tapping outside
 */
const CustomAlert = ({
  visible,
  title,
  message,
  buttons = [{ text: 'OK', onPress: () => {} }],
  onClose,
  type = 'info',
  dismissable = true
}) => {
  const [fadeAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.9));

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 200,
          useNativeDriver: true
        })
      ]).start();
    }
  }, [visible, fadeAnim, scaleAnim]);

  // Get alert icon based on type
  const getAlertIcon = () => {
    switch (type) {
      case 'success':
        return '✓';
      case 'error':
        return '✕';
      case 'warning':
        return '⚠';
      case 'info':
      default:
        return 'ℹ';
    }
  };

  // Get alert color based on type
  const getAlertColor = () => {
    switch (type) {
      case 'success':
        return colors.success || '#4CAF50';
      case 'error':
        return colors.error || '#F44336';
      case 'warning':
        return colors.warning || '#FF9800';
      case 'info':
      default:
        return colors.primary;
    }
  };

  // Handle button press
  const handleButtonPress = (button) => {
    if (button.onPress) {
      button.onPress();
    }
    if (onClose) {
      // Delay closing the alert slightly to make sure the onPress is executed first
      setTimeout(() => {
        onClose();
      }, 100);
    }
  };

  // Handle outside press
  const handleOutsidePress = () => {
    if (dismissable && onClose) {
      onClose();
    }
  };

  return (
    <Modal
      transparent
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleOutsidePress}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <Animated.View
              style={[
                styles.alertContainer,
                {
                  opacity: fadeAnim,
                  transform: [{ scale: scaleAnim }]
                }
              ]}
            >
              <View style={[styles.iconContainer, { backgroundColor: getAlertColor() }]}>
                <Text style={styles.icon}>{getAlertIcon()}</Text>
              </View>
              
              <Text style={styles.title}>{title}</Text>
              
              {message ? <Text style={styles.message}>{message}</Text> : null}
              
              <View style={[
                styles.buttonContainer,
                buttons.length > 2 && styles.buttonContainerVertical
              ]}>
                {buttons.map((button, index) => (
                  <Button
                    key={index}
                    title={button.text}
                    onPress={() => handleButtonPress(button)}
                    variant={button.style === 'cancel' ? 'outline' : 'primary'}
                    style={[
                      styles.button,
                      buttons.length <= 2 && index < buttons.length - 1 && styles.buttonMarginRight,
                      buttons.length > 2 && index < buttons.length - 1 && styles.buttonMarginBottom
                    ]}
                  />
                ))}
              </View>
            </Animated.View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  alertContainer: {
    width: width - 60,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.medium,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.medium,
  },
  icon: {
    color: colors.white,
    fontSize: 24,
    fontWeight: 'bold',
  },
  title: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.small,
    textAlign: 'center',
  },
  message: {
    ...textStyles.body1,
    color: colors.textLight,
    marginBottom: spacing.medium,
    textAlign: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
  },
  buttonContainerVertical: {
    flexDirection: 'column',
  },
  button: {
    flex: 1,
    minWidth: 100,
  },
  buttonMarginRight: {
    marginRight: spacing.small,
  },
  buttonMarginBottom: {
    marginBottom: spacing.small,
  },
});

export default CustomAlert;

import React from 'react';
import { StatusBar } from 'react-native';
import { colors } from '../../theme/colors';

/**
 * StatusBarManager - centralized component for managing status bar appearance
 * @param {Object} props Component props
 * @param {string} props.backgroundColor Background color for the status bar
 * @param {'default' | 'light-content' | 'dark-content'} props.barStyle Status bar content style
 */
const StatusBarManager = ({ 
  backgroundColor = colors.primary, 
  barStyle = 'light-content' 
}) => {
  return <StatusBar backgroundColor={backgroundColor} barStyle={barStyle} />;
};

export default StatusBarManager;
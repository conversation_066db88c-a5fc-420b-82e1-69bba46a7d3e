import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Keyboard,
  TouchableWithoutFeedback
} from 'react-native';
import { colors } from '../../theme/colors';
import { textStyles } from '../../theme/typography';
import { spacing } from '../../theme/spacing';

/**
 * OTP Input component for verification screens
 *
 * @param {number} length - Number of OTP digits
 * @param {function} onCodeFilled - Function called when all digits are filled
 * @param {boolean} autoFocus - Whether to focus the first input automatically
 */
const OTPInput = ({
  length = 6,
  onCodeFilled,
  autoFocus = true
}) => {
  const [code, setCode] = useState(Array(length).fill(''));
  const inputs = useRef([]);

  useEffect(() => {
    // Check if code is complete and call onCodeFilled
    if (code.every(digit => digit !== '')) {
      onCodeFilled && onCodeFilled(code.join(''));
    }
  }, [code, onCodeFilled]);

  const handleChange = (text, index) => {
    // Update the code array
    const newCode = [...code];
    newCode[index] = text;
    setCode(newCode);

    // Move to next input if current input is filled
    if (text !== '' && index < length - 1) {
      inputs.current[index + 1].focus();
    }
  };

  const handleKeyPress = (e, index) => {
    // Move to previous input on backspace if current input is empty
    if (e.nativeEvent.key === 'Backspace' && index > 0 && code[index] === '') {
      inputs.current[index - 1].focus();
    }
  };

  const handleFocus = (index) => {
    // Select all text when focused
    inputs.current[index].setNativeProps({ selection: { start: 0, end: 1 } });
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View style={styles.container}>
        {Array(length).fill(0).map((_, index) => (
          <TextInput
            key={index}
            ref={ref => inputs.current[index] = ref}
            style={styles.input}
            value={code[index]}
            onChangeText={text => handleChange(text, index)}
            onKeyPress={e => handleKeyPress(e, index)}
            onFocus={() => handleFocus(index)}
            keyboardType="number-pad"
            maxLength={1}
            selectTextOnFocus
            autoFocus={autoFocus && index === 0}
          />
        ))}
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  input: {
    width: 50,
    height: 60,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    backgroundColor: colors.white,
  },
});

export default OTPInput;

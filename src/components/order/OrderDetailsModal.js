import { View, Text, StyleSheet, Modal, Sc<PERSON>View, TouchableOpacity } from "react-native"
import { colors } from "../../theme/colors"
import { spacing } from "../../theme/spacing"
import { textStyles } from "../../theme/typography"
import Button from "../common/Button"

const OrderDetailsModal = ({ visible, order, onClose }) => {
  // Function to format date
  const formatDate = (dateString) => {
    const options = { day: "numeric", month: "long", year: "numeric" }
    return new Date(dateString).toLocaleDateString("en-US", options)
  }

  // Function to get the status color
  const getStatusColor = (status) => {
    switch (status.toUpperCase()) {
      case "PENDING":
        return colors.warning
      case "APPROVED":
        return colors.primary
      case "SHIPPED":
        return colors.info
      case "DELIVERED":
        return colors.success
      case "CANCELLED":
        return colors.error
      default:
        return colors.textLight
    }
  }

  if (!order) return null

  return (
    <Modal visible={visible} animationType="slide" transparent={true} onRequestClose={onClose}>
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.title}>Order Details</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.scrollContent}>
            <View style={styles.orderHeader}>
              <View>
                <Text style={styles.orderNumber}>Order #{order.orderNumber}</Text>
                <Text style={styles.date}>{formatDate(order.createdAt)}</Text>
              </View>

              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
                <Text style={styles.statusText}>{order.status}</Text>
              </View>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Items</Text>
              {order.items && order.items.map((item, index) => (
                <View key={index} style={styles.itemRow}>
                  <Text style={styles.quantity}>{item.quantity}x</Text>
                  <Text style={styles.itemName}>{`${item.type} ${item.gsm}gsm BF${item.bf}`}</Text>
                  <Text style={styles.itemPrice}>₹{item.pricePerRoll}</Text>
                </View>
              ))}
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Shipping Address</Text>
              <Text style={styles.addressText}>{order.shippingAddress.addressLine1}</Text>
              <Text style={styles.addressText}>
                {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}
              </Text>
              <Text style={styles.addressText}>{order.shippingAddress.country}</Text>
            </View>

            {order.notes && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Order Notes</Text>
                <Text style={styles.notesText}>{order.notes}</Text>
              </View>
            )}

            <View style={styles.pricingSection}>
              <View style={styles.pricingRow}>
                <Text style={styles.pricingLabel}>Subtotal:</Text>
                <Text style={styles.pricingValue}>₹{order.subtotal || order.totalAmount}</Text>
              </View>

              {order.taxes && (
                <View style={styles.pricingRow}>
                  <Text style={styles.pricingLabel}>Taxes:</Text>
                  <Text style={styles.pricingValue}>₹{order.taxes}</Text>
                </View>
              )}

              {order.shipping && (
                <View style={styles.pricingRow}>
                  <Text style={styles.pricingLabel}>Shipping:</Text>
                  <Text style={styles.pricingValue}>₹{order.shipping}</Text>
                </View>
              )}

              <View style={styles.pricingRowTotal}>
                <Text style={styles.totalLabel}>Total Amount:</Text>
                <Text style={styles.totalValue}>₹{order.totalAmount}</Text>
              </View>
            </View>
          </ScrollView>

          <View style={styles.footer}>
            {order.status === "PENDING" && (
              <Button
                title="Cancel Order"
                variant="outline"
                onPress={() => {
                  // Handle order cancellation
                  onClose()
                }}
                style={styles.cancelButton}
              />
            )}
            <Button title="Close" onPress={onClose} style={styles.closeActionButton} />
          </View>
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    backgroundColor: colors.white,
    borderRadius: 12,
    width: "90%",
    maxHeight: "80%",
    padding: spacing.medium,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.medium,
    paddingBottom: spacing.small,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    ...textStyles.heading3,
    color: colors.textDark,
  },
  closeButton: {
    padding: spacing.small,
  },
  closeButtonText: {
    fontSize: 18,
    color: colors.textLight,
  },
  scrollContent: {
    flex: 1,
  },
  orderHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.medium,
  },
  orderNumber: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "600",
  },
  date: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  statusBadge: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    borderRadius: 4,
  },
  statusText: {
    ...textStyles.caption,
    color: colors.white,
    fontWeight: "500",
  },
  section: {
    marginBottom: spacing.large,
  },
  sectionTitle: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "600",
    marginBottom: spacing.small,
  },
  itemRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.small,
  },
  quantity: {
    ...textStyles.body2,
    color: colors.textLight,
    width: 30,
  },
  itemName: {
    ...textStyles.body2,
    color: colors.textDark,
    flex: 1,
  },
  itemPrice: {
    ...textStyles.body2,
    color: colors.textDark,
    fontWeight: "500",
  },
  addressText: {
    ...textStyles.body2,
    color: colors.textDark,
    marginBottom: spacing.tiny,
  },
  notesText: {
    ...textStyles.body2,
    color: colors.textDark,
  },
  pricingSection: {
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.medium,
    marginBottom: spacing.medium,
  },
  pricingRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.small,
  },
  pricingLabel: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  pricingValue: {
    ...textStyles.body2,
    color: colors.textDark,
  },
  pricingRowTotal: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.small,
    paddingTop: spacing.small,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  totalLabel: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "600",
  },
  totalValue: {
    ...textStyles.body1,
    color: colors.primary,
    fontWeight: "600",
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: spacing.medium,
  },
  cancelButton: {
    flex: 1,
    marginRight: spacing.small,
  },
  closeActionButton: {
    flex: 1,
    marginLeft: spacing.small,
  },
})

export default OrderDetailsModal

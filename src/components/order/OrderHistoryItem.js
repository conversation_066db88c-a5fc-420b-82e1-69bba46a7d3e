import { View, Text, StyleSheet, TouchableOpacity } from "react-native"
import { colors } from "../../theme/colors"
import { spacing } from "../../theme/spacing"
import { textStyles } from "../../theme/typography"

const OrderHistoryItem = ({ order, onViewDetails }) => {
  // Function to format date
  const formatDate = (dateString) => {
    const options = { day: "numeric", month: "long", year: "numeric" }
    return new Date(dateString).toLocaleDateString("en-US", options)
  }

  // Function to get the status color
  const getStatusColor = (status) => {
    switch (status.toUpperCase()) {
      case "PENDING":
        return colors.warning
      case "APPROVED":
        return colors.primary
      case "SHIPPED":
        return colors.info
      case "DELIVERED":
        return colors.success
      case "CANCELLED":
        return colors.error
      default:
        return colors.textLight
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View>
          <Text style={styles.orderNumber}>Order #{order.orderNumber}</Text>
          <Text style={styles.date}>{formatDate(order.createdAt)}</Text>
        </View>

        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(order.status) }]}>
          <Text style={styles.statusText}>{order.status}</Text>
        </View>
      </View>

      <View style={styles.itemsContainer}>
        {order.items && order.items.slice(0, 2).map((item, index) => (
          <View key={index} style={styles.itemRow}>
            <Text style={styles.quantity}>{item.quantity}x</Text>
            <Text style={styles.itemName}>{`${item.type} ${item.gsm}gsm BF${item.bf}`}</Text>
            <Text style={styles.itemPrice}>₹{item.pricePerRoll}</Text>
          </View>
        ))}

        {order.items && order.items.length > 2 && (
          <Text style={styles.moreItems}>+ {order.items.length - 2} more items</Text>
        )}
      </View>

      <View style={styles.footer}>
        <Text style={styles.totalText}>Total Amount</Text>
        <Text style={styles.totalAmount}>₹{order.totalAmount}</Text>

        <TouchableOpacity style={styles.viewDetailsButton} onPress={() => onViewDetails(order)}>
          <Text style={styles.viewDetailsText}>View Details {">"}</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 8,
    marginVertical: spacing.small,
    padding: spacing.medium,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.medium,
  },
  orderNumber: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "600",
  },
  date: {
    ...textStyles.caption,
    color: colors.textLight,
  },
  statusBadge: {
    paddingHorizontal: spacing.small,
    paddingVertical: spacing.tiny,
    borderRadius: 4,
  },
  statusText: {
    ...textStyles.caption,
    color: colors.white,
    fontWeight: "500",
  },
  itemsContainer: {
    marginBottom: spacing.medium,
  },
  itemRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.small,
  },
  quantity: {
    ...textStyles.body2,
    color: colors.textLight,
    width: 30,
  },
  itemName: {
    ...textStyles.body2,
    color: colors.textDark,
    flex: 1,
  },
  itemPrice: {
    ...textStyles.body2,
    color: colors.textDark,
    fontWeight: "500",
  },
  moreItems: {
    ...textStyles.caption,
    color: colors.textLight,
    fontStyle: "italic",
  },
  footer: {
    flexDirection: "row",
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.medium,
  },
  totalText: {
    ...textStyles.body2,
    color: colors.textLight,
  },
  totalAmount: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "600",
    marginLeft: spacing.small,
  },
  viewDetailsButton: {
    marginLeft: "auto",
  },
  viewDetailsText: {
    ...textStyles.body2,
    color: colors.primary,
    fontWeight: "500",
  },
})

export default OrderHistoryItem

import { View, Text, StyleSheet, TouchableOpacity } from "react-native"
import { colors } from "../../theme/colors"
import { spacing } from "../../theme/spacing"
import { textStyles } from "../../theme/typography"

const CartItem = ({ item, onEdit, onDelete }) => {
  // Safely access stock properties with fallbacks
  const stock = item?.stock || {}
  const type = stock.type || "Unknown"
  const price = stock.pricePerRoll || 0
  const gsm = stock.gsm || "N/A"
  const bf = stock.bf || "N/A"
  const quantity = item.quantity || 0

  // Format price with commas for better readability
  const formatPrice = (price) => {
    return price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",")
  }

  return (
    <View style={styles.container}>
      <View style={styles.details}>
        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Text style={styles.label}>Type:</Text>
            <Text style={styles.value}>{type}</Text>
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.label}>Price:</Text>
            <Text style={styles.price}>₹{formatPrice(price)} per Kg</Text>
          </View>
        </View>

        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Text style={styles.label}>GSM:</Text>
            <Text style={styles.value}>{gsm}</Text>
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.label}>BF:</Text>
            <Text style={styles.value}>{bf}</Text>
          </View>
        </View>

        <View style={styles.row}>
          <View style={styles.labelContainer}>
            <Text style={styles.label}>Number of Rolls:</Text>
            <Text style={styles.value}>{quantity}</Text>
          </View>

          <View style={styles.labelContainer}>
            <Text style={styles.label}>Subtotal:</Text>
            <Text style={styles.price}>₹{formatPrice(price * quantity)}</Text>
          </View>
        </View>
      </View>

      <View style={styles.actions}>
        <TouchableOpacity style={styles.actionButton} onPress={() => onEdit(item)}>
          <Text style={styles.actionText}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity style={[styles.actionButton, styles.deleteButton]} onPress={() => onDelete(item)}>
          <Text style={styles.deleteText}>Remove</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 8,
    marginVertical: spacing.small,
    padding: spacing.medium,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  details: {
    marginBottom: spacing.medium,
    gap: spacing.small,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  labelContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  label: {
    ...textStyles.body2,
    color: colors.textLight,
    marginRight: spacing.small,
  },
  value: {
    ...textStyles.body2,
    color: colors.textDark,
    fontWeight: "500",
  },
  price: {
    ...textStyles.body2,
    color: colors.secondary,
    fontWeight: "500",
  },
  actions: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: spacing.medium,
  },
  actionButton: {
    paddingVertical: spacing.small,
    paddingHorizontal: spacing.medium,
    borderRadius: 4,
    backgroundColor: colors.card,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    marginHorizontal: spacing.tiny,
  },
  actionText: {
    ...textStyles.button,
    color: colors.primary,
  },
  deleteButton: {
    backgroundColor: colors.lightGray,
  },
  deleteText: {
    ...textStyles.button,
    color: colors.error,
  },
})

export default CartItem

import { View, Image, TouchableOpacity, StyleSheet } from "react-native"
import { colors } from "../../theme/colors"
import { spacing } from "../../theme/spacing"

const AttachmentItem = ({ uri, onRemove }) => {
  return (
    <View style={styles.container}>
      <Image source={{ uri }} style={styles.image} />
      <TouchableOpacity style={styles.removeButton} onPress={onRemove}>
        <View style={styles.removeIcon}>
          <View style={styles.crossLine1} />
          <View style={styles.crossLine2} />
        </View>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginRight: spacing.small,
    marginBottom: spacing.small,
    overflow: "hidden",
    position: "relative",
  },
  image: {
    width: "100%",
    height: "100%",
  },
  removeButton: {
    position: "absolute",
    top: 4,
    right: 4,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    alignItems: "center",
    justifyContent: "center",
  },
  removeIcon: {
    width: 10,
    height: 10,
    alignItems: "center",
    justifyContent: "center",
  },
  crossLine1: {
    position: "absolute",
    width: 10,
    height: 2,
    backgroundColor: colors.white,
    transform: [{ rotate: "45deg" }],
  },
  crossLine2: {
    position: "absolute",
    width: 10,
    height: 2,
    backgroundColor: colors.white,
    transform: [{ rotate: "-45deg" }],
  },
})

export default AttachmentItem

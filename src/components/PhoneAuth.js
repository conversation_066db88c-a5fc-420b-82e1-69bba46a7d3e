import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator } from 'react-native';
import { usePhoneAuth } from '../utils/firebase';

const PhoneAuth = () => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [verificationSent, setVerificationSent] = useState(false);
  const [authenticated, setAuthenticated] = useState(false);
  
  const { sendVerificationCode, verifyCode, loading, error, confirm } = usePhoneAuth();
  
  const handleSendCode = async () => {
    const success = await sendVerificationCode(phoneNumber);
    if (success) {
      setVerificationSent(true);
    }
  };
  
  const handleVerifyCode = async () => {
    const success = await verifyCode(verificationCode);
    if (success) {
      setAuthenticated(true);
    }
  };
  
  const resetAuth = () => {
    setPhoneNumber('');
    setVerificationCode('');
    setVerificationSent(false);
    setAuthenticated(false);
  };
  
  if (authenticated) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Authentication Successful!</Text>
        <Text style={styles.subtitle}>You are now logged in.</Text>
        <TouchableOpacity style={styles.button} onPress={resetAuth}>
          <Text style={styles.buttonText}>Sign Out</Text>
        </TouchableOpacity>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Phone Authentication</Text>
      
      {error && <Text style={styles.error}>{error}</Text>}
      
      {!verificationSent ? (
        <>
          <Text style={styles.label}>Enter your phone number:</Text>
          <TextInput
            style={styles.input}
            value={phoneNumber}
            onChangeText={setPhoneNumber}
            placeholder="+1234567890"
            keyboardType="phone-pad"
            editable={!loading}
          />
          <TouchableOpacity 
            style={[styles.button, loading && styles.buttonDisabled]} 
            onPress={handleSendCode}
            disabled={loading || !phoneNumber}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Send Verification Code</Text>
            )}
          </TouchableOpacity>
        </>
      ) : (
        <>
          <Text style={styles.label}>Enter verification code:</Text>
          <TextInput
            style={styles.input}
            value={verificationCode}
            onChangeText={setVerificationCode}
            placeholder="123456"
            keyboardType="number-pad"
            editable={!loading}
          />
          <TouchableOpacity 
            style={[styles.button, loading && styles.buttonDisabled]} 
            onPress={handleVerifyCode}
            disabled={loading || !verificationCode}
          >
            {loading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.buttonText}>Verify Code</Text>
            )}
          </TouchableOpacity>
          <TouchableOpacity 
            style={styles.textButton} 
            onPress={() => setVerificationSent(false)}
            disabled={loading}
          >
            <Text style={styles.textButtonText}>Change Phone Number</Text>
          </TouchableOpacity>
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginHorizontal: 20,
    marginVertical: 10,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    marginBottom: 20,
    textAlign: 'center',
    color: '#666',
  },
  label: {
    fontSize: 16,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#4285F4',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonDisabled: {
    backgroundColor: '#A1C0F7',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  textButton: {
    padding: 10,
    alignItems: 'center',
  },
  textButtonText: {
    color: '#4285F4',
    fontSize: 14,
  },
  error: {
    color: 'red',
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default PhoneAuth;

import React from "react"
import { View, StyleSheet } from "react-native"
import Icon from "react-native-vector-icons/Feather"
import { colors } from "../../theme/colors"

// Home Icon
export const HomeIcon = ({ focused }) => {
  return (
    <Icon 
      name="home" 
      size={24} 
      color={focused ? colors.primary : colors.gray}
    />
  )
}

// Cart Icon
export const CartIcon = ({ focused }) => {
  return (
    <Icon 
      name="shopping-cart" 
      size={24} 
      color={focused ? colors.primary : colors.gray}
    />
  )
}

// Enquiries Icon
export const EnquiriesIcon = ({ focused }) => {
  return (
    <Icon 
      name="message-circle" 
      size={24} 
      color={focused ? colors.primary : colors.gray}
    />
  )
}

// Orders Icon
export const OrdersIcon = ({ focused }) => {
  return (
    <Icon 
      name="package" 
      size={24} 
      color={focused ? colors.primary : colors.gray}
    />
  )
}

// Profile Icon
export const ProfileIcon = ({ focused }) => {
  return (
    <Icon 
      name="user" 
      size={24} 
      color={focused ? colors.primary : colors.gray}
    />
  )
}

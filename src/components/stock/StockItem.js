import { View, Text, StyleSheet, TouchableOpacity } from "react-native"
import { colors } from "../../theme/colors"
import { spacing } from "../../theme/spacing"
import { textStyles } from "../../theme/typography"

const StockItem = ({ item, onPress }) => {
  return (
    <TouchableOpacity style={styles.container} onPress={() => onPress(item)} activeOpacity={0.7}>
      <View style={styles.row}>
        <View style={styles.column}>
          <Text style={styles.label}>Type</Text>
          <Text style={styles.value}>{item.type}</Text>
        </View>
        
        <View style={styles.column}>
          <Text style={styles.label}>GSM</Text>
          <Text style={styles.value}>{item.gsm}</Text>
        </View>
        
        <View style={styles.column}>
          <Text style={styles.label}>BF</Text>
          <Text style={styles.value}>{item.bf}</Text>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 0,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    paddingVertical: spacing.small,
    marginHorizontal: 0,
  },
  row: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: spacing.medium,
  },
  column: {
    alignItems: "center",
    flex: 1,
  },
  label: {
    ...textStyles.body2,
    color: colors.textLight,
    marginBottom: spacing.tiny,
  },
  value: {
    ...textStyles.body1,
    color: colors.textDark,
    fontWeight: "500",
  }
})

export default StockItem

import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useFirebaseMessaging } from '../utils/firebase';

const NotificationDisplay = () => {
  const { notification } = useFirebaseMessaging();
  
  useEffect(() => {
    if (notification) {
      console.log('New notification received:', notification);
    }
  }, [notification]);
  
  if (!notification) {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Notifications</Text>
        <Text style={styles.emptyText}>No notifications received yet.</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Notifications</Text>
      <ScrollView style={styles.notificationContainer}>
        <Text style={styles.notificationTitle}>
          {notification.notification?.title || 'New Message'}
        </Text>
        <Text style={styles.notificationBody}>
          {notification.notification?.body || 'You have a new notification'}
        </Text>
        
        {notification.data && Object.keys(notification.data).length > 0 && (
          <>
            <Text style={styles.dataTitle}>Additional Data:</Text>
            {Object.entries(notification.data).map(([key, value]) => (
              <Text key={key} style={styles.dataItem}>
                <Text style={styles.dataKey}>{key}:</Text> {value}
              </Text>
            ))}
          </>
        )}
        
        <Text style={styles.timestamp}>
          Received: {new Date().toLocaleString()}
        </Text>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginHorizontal: 20,
    marginVertical: 10,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 20,
  },
  notificationContainer: {
    maxHeight: 300,
    borderWidth: 1,
    borderColor: '#eee',
    borderRadius: 8,
    padding: 15,
  },
  notificationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  notificationBody: {
    fontSize: 16,
    marginBottom: 15,
    color: '#444',
  },
  dataTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginTop: 10,
    marginBottom: 5,
    color: '#555',
  },
  dataItem: {
    fontSize: 14,
    marginBottom: 5,
    color: '#666',
  },
  dataKey: {
    fontWeight: 'bold',
  },
  timestamp: {
    fontSize: 12,
    color: '#888',
    marginTop: 15,
    textAlign: 'right',
    fontStyle: 'italic',
  },
});

export default NotificationDisplay;

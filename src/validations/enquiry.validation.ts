import Joi from 'joi';

// Define contact preference and enquiry status values
const CONTACT_PREFERENCES = ['EMAIL', 'PHONE'];
const ENQUIRY_STATUSES = ['PENDING', 'RESPONDED', 'CLOSED'];

// Create enquiry validation schema
export const createEnquirySchema = Joi.object({
  subject: Joi.string().trim().min(5).max(100).required()
    .messages({
      'string.empty': 'Subject is required',
      'string.min': 'Subject must be at least 5 characters long',
      'string.max': 'Subject cannot exceed 100 characters',
      'any.required': 'Subject is required',
    }),
  message: Joi.string().trim().min(10).max(1000).required()
    .messages({
      'string.empty': 'Message is required',
      'string.min': 'Message must be at least 10 characters long',
      'string.max': 'Message cannot exceed 1000 characters',
      'any.required': 'Message is required',
    }),
  contactPreference: Joi.string().valid(...CONTACT_PREFERENCES).required()
    .messages({
      'any.only': `Contact preference must be one of: ${CONTACT_PREFERENCES.join(', ')}`,
      'any.required': 'Contact preference is required',
    }),
});

// Enquiry query validation schema
export const enquiryQuerySchema = Joi.object({
  page: Joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  status: Joi.string().valid(...ENQUIRY_STATUSES)
    .messages({
      'any.only': `Status must be one of: ${ENQUIRY_STATUSES.join(', ')}`,
    }),
  sortBy: Joi.string().valid('createdAt', 'subject', 'status')
    .messages({
      'any.only': 'Sort by must be one of: createdAt, subject, status',
    }),
  sortOrder: Joi.string().valid('asc', 'desc')
    .messages({
      'any.only': 'Sort order must be one of: asc, desc',
    }),
});

// Enquiry response validation schema (for admin)
export const enquiryResponseSchema = Joi.object({
  response: Joi.string().trim().min(10).max(1000).required()
    .messages({
      'string.empty': 'Response is required',
      'string.min': 'Response must be at least 10 characters long',
      'string.max': 'Response cannot exceed 1000 characters',
      'any.required': 'Response is required',
    }),
});

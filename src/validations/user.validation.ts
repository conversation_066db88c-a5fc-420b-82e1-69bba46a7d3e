import Joi from 'joi';

// Profile update validation schema
export const profileUpdateSchema = Joi.object({
  contactPerson: Joi.string().min(3).max(50)
    .messages({
      'string.min': 'Contact person name must be at least 3 characters long',
      'string.max': 'Contact person name cannot exceed 50 characters',
    }),
  contactNumber: Joi.string().pattern(/^[6-9]\d{9}$/)
    .messages({
      'string.pattern.base': 'Please enter a valid 10-digit Indian mobile number',
    }),
  email: Joi.string().email()
    .messages({
      'string.email': 'Please enter a valid email address',
    }),
}).min(1).messages({
  'object.min': 'At least one field is required for update',
});

// Change password validation schema
export const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required()
    .messages({
      'any.required': 'Current password is required',
    }),
  newPassword: Joi.string().min(8).required()
    .messages({
      'string.min': 'New password must be at least 8 characters long',
      'any.required': 'New password is required',
    }),
}).messages({
  'object.unknown': 'Invalid field in request',
});

// Notification pagination validation schema
export const notificationQuerySchema = Joi.object({
  page: Joi.number().integer().min(1)
    .messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1',
    }),
  limit: Joi.number().integer().min(1).max(100)
    .messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100',
    }),
  read: Joi.boolean()
    .messages({
      'boolean.base': 'Read status must be a boolean',
    }),
});

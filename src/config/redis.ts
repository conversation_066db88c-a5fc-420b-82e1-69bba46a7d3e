import { createClient } from 'redis';
import { config } from 'dotenv';
config();
console.log("redis url", process.env.REDIS_URL);
// Configure Redis client with options for cloud or local
const redisClient = createClient({
  url: process.env.REDIS_URL || "redis://localhost:6379",
  // Add TLS options for secure Redis connections (like Upstash)
  socket: process.env.REDIS_URL?.startsWith('rediss://')
    ? {
        tls: true,
        rejectUnauthorized: false, // Needed for some cloud Redis providers
        host: process.env.REDIS_HOST || "happy-marlin-28883.upstash.io", // Add host for TLS
      }
    : undefined
});

redisClient.on('error', (err) => {
  console.error('Redis Client Error', err);
});

export const connectRedis = async () => {
  try {
    await redisClient.connect();
    console.log('Redis connected successfully');

    // Test Redis connection
    await redisClient.set('test', 'Connected to Redis');
    const testResult = await redisClient.get('test');
    console.log('Redis test result:', testResult);
  } catch (error) {
    console.error('Redis connection error:', error);
    // Don't fail the app startup if Redis is not available
    console.log('Continuing without Redis - some features may be limited');
  }
};

export default redisClient;

import prisma from '../config/database';
import { NotificationType } from '@prisma/client';
import { AppError } from '../utils/errorHandler';

/**
 * Create a new notification
 * @param userId - User ID to create notification for
 * @param type - Notification type
 * @param title - Notification title
 * @param message - Notification message
 * @returns Created notification
 */
export const createNotification = async (
  userId: string,
  type: NotificationType,
  title: string,
  message: string
) => {
  try {
    // Create notification without checking user
    // This is a simplification to avoid potential issues
    const notification = await prisma.notification.create({
      data: {
        userId,
        type,
        title,
        message,
      },
    });

    return notification;
  } catch (error) {
    console.error('Error creating notification:', error);
    // Just log the error but don't throw it to prevent API failures
    return null;
  }
};

/**
 * Get user notifications with pagination
 * @param userId - User ID to get notifications for
 * @param page - Page number (default: 1)
 * @param limit - Number of items per page (default: 10)
 * @param isRead - Filter by read status (optional)
 * @returns Paginated notifications
 */
export const getUserNotifications = async (
  userId: string,
  page = 1,
  limit = 10,
  isRead?: boolean
) => {
  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = { userId };
  if (isRead !== undefined) {
    where.isRead = isRead;
  }

  // Get notifications with pagination
  const [notifications, total] = await Promise.all([
    prisma.notification.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    }),
    prisma.notification.count({ where }),
  ]);

  // Calculate pagination info
  const totalPages = Math.ceil(total / limit);
  const hasNextPage = page < totalPages;
  const hasPrevPage = page > 1;

  return {
    notifications,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage,
    },
  };
};

/**
 * Mark notification as read
 * @param notificationId - Notification ID
 * @param userId - User ID (for authorization)
 * @returns Updated notification
 */
export const markNotificationAsRead = async (notificationId: string, userId: string) => {
  // Find notification and check ownership
  const notification = await prisma.notification.findFirst({
    where: {
      id: notificationId,
      userId,
    },
  });

  if (!notification) {
    throw new AppError('Notification not found', 404);
  }

  // If already read, return as is
  if (notification.isRead) {
    return notification;
  }

  // Update notification
  return prisma.notification.update({
    where: { id: notificationId },
    data: {
      isRead: true,
      readAt: new Date(),
    },
  });
};

/**
 * Mark all notifications as read for a user
 * @param userId - User ID
 * @returns Count of updated notifications
 */
export const markAllNotificationsAsRead = async (userId: string) => {
  // Update all unread notifications for the user
  const result = await prisma.notification.updateMany({
    where: {
      userId,
      isRead: false,
    },
    data: {
      isRead: true,
      readAt: new Date(),
    },
  });

  return { count: result.count };
};

/**
 * Delete a notification
 * @param notificationId - Notification ID
 * @param userId - User ID (for authorization)
 * @returns Deleted notification
 */
export const deleteNotification = async (notificationId: string, userId: string) => {
  // Find notification and check ownership
  const notification = await prisma.notification.findFirst({
    where: {
      id: notificationId,
      userId,
    },
  });

  if (!notification) {
    throw new AppError('Notification not found', 404);
  }

  // Delete notification
  return prisma.notification.delete({
    where: { id: notificationId },
  });
};

/**
 * Create account approval notification
 * @param userId - User ID
 * @returns Created notification
 */
export const createAccountApprovalNotification = async (userId: string) => {
  return createNotification(
    userId,
    'ACCOUNT_APPROVAL',
    'Account Approved',
    'Your account has been approved. You can now place orders.'
  );
};

/**
 * Create account rejection notification
 * @param userId - User ID
 * @param reason - Rejection reason
 * @returns Created notification
 */
export const createAccountRejectionNotification = async (userId: string, reason: string) => {
  return createNotification(
    userId,
    'ACCOUNT_APPROVAL',
    'Account Application Status',
    `Your account application has not been approved. Reason: ${reason}`
  );
};

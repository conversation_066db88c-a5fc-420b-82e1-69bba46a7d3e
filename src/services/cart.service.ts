import prisma from '../config/database';
import { AppError } from '../utils/errorHandler';
import { checkStockAvailability } from './stock.service';
import { CartItem } from '@prisma/client';

/**
 * Get user's cart items
 * @param userId - User ID
 * @param includeStock - Whether to include stock details
 * @returns Cart items with optional stock details
 */
export const getUserCart = async (userId: string, includeStock = true) => {
  const cartItems = await prisma.cartItem.findMany({
    where: { userId },
    include: {
      stock: includeStock,
    },
    orderBy: { createdAt: 'desc' },
  });

  // Calculate total price
  const total = cartItems.reduce((sum, item) => {
    return sum + (item.quantity * (item.stock?.pricePerRoll || 0));
  }, 0);

  return {
    items: cartItems,
    total,
    itemCount: cartItems.length,
  };
};

/**
 * Add item to cart
 * @param userId - User ID
 * @param stockId - Stock ID
 * @param quantity - Quantity to add
 * @returns Added cart item
 */
export const addToCart = async (
  userId: string,
  stockId: string,
  quantity: number
): Promise<CartItem> => {
  // Check if stock exists and has sufficient quantity
  const isAvailable = await checkStockAvailability(stockId, quantity);
  if (!isAvailable) {
    throw new AppError('Insufficient stock available', 400);
  }

  // Check if item already exists in cart
  const existingCartItem = await prisma.cartItem.findUnique({
    where: {
      userId_stockId: {
        userId,
        stockId,
      },
    },
  });

  if (existingCartItem) {
    // Update quantity if item already exists
    return updateCartItem(existingCartItem.id, userId, existingCartItem.quantity + quantity);
  }

  // Create new cart item
  return prisma.cartItem.create({
    data: {
      userId,
      stockId,
      quantity,
    },
  });
};

/**
 * Update cart item quantity
 * @param cartItemId - Cart item ID
 * @param userId - User ID (for authorization)
 * @param quantity - New quantity
 * @returns Updated cart item
 */
export const updateCartItem = async (
  cartItemId: string,
  userId: string,
  quantity: number
): Promise<CartItem> => {
  // Find cart item and check ownership
  const cartItem = await prisma.cartItem.findFirst({
    where: {
      id: cartItemId,
      userId,
    },
    include: {
      stock: true,
    },
  });

  if (!cartItem) {
    throw new AppError('Cart item not found', 404);
  }

  // Check if stock has sufficient quantity
  const isAvailable = await checkStockAvailability(cartItem.stockId, quantity);
  if (!isAvailable) {
    throw new AppError('Insufficient stock available', 400);
  }

  // Update cart item
  return prisma.cartItem.update({
    where: { id: cartItemId },
    data: { quantity },
  });
};

/**
 * Remove item from cart
 * @param cartItemId - Cart item ID
 * @param userId - User ID (for authorization)
 * @returns Removed cart item
 */
export const removeFromCart = async (
  cartItemId: string,
  userId: string
): Promise<CartItem> => {
  // Find cart item and check ownership
  const cartItem = await prisma.cartItem.findFirst({
    where: {
      id: cartItemId,
      userId,
    },
  });

  if (!cartItem) {
    throw new AppError('Cart item not found', 404);
  }

  // Delete cart item
  return prisma.cartItem.delete({
    where: { id: cartItemId },
  });
};

/**
 * Clear user's cart
 * @param userId - User ID
 * @returns Number of items removed
 */
export const clearCart = async (userId: string): Promise<{ count: number }> => {
  const result = await prisma.cartItem.deleteMany({
    where: { userId },
  });

  return { count: result.count };
};

/**
 * Check if cart is empty
 * @param userId - User ID
 * @returns Boolean indicating if cart is empty
 */
export const isCartEmpty = async (userId: string): Promise<boolean> => {
  const count = await prisma.cartItem.count({
    where: { userId },
  });

  return count === 0;
};

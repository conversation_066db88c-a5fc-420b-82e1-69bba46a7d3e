import axios from 'axios';
import crypto from 'crypto';

// Replace with your Firebase Web API Key
const FIREBASE_API_KEY = process.env.FIREBASE_API_KEY;

// Define interfaces for the OTP data structure
interface OTPData {
  sessionInfo: string;
  expiresAt: number;
}

// Define response interfaces
interface FirebaseSendOTPResponse {
  sessionInfo: string;
}

interface FirebaseVerifyOTPResponse {
  idToken?: string;
}

interface SendPhoneOTPResult {
  verificationId: string;
}

interface VerifyPhoneOTPResult {
  success: boolean;
  idToken: string;
}

// In-memory store for session info (use a database in production)
const otpStore = new Map<string, OTPData>();

// Send SMS with OTP
export const sendPhoneOTP = async (phoneNumber: string, recaptchaToken: string): Promise<SendPhoneOTPResult> => {
  try {
    // Validate phone number format
    if (!phoneNumber.startsWith('+')) {
      throw new Error('Phone number must include country code (e.g., +91)');
    }

    // Request Firebase to send SMS
    const response = await axios.post<FirebaseSendOTPResponse>(
      `https://www.googleapis.com/identitytoolkit/v3/relyingparty/sendVerificationCode?key=${FIREBASE_API_KEY}`,
      {
        phoneNumber,
        recaptchaToken,
      }
    );

    const sessionInfo = response.data.sessionInfo;
    const verificationId = crypto.randomBytes(16).toString('hex');

    // Store session info with expiration (10 minutes)
    otpStore.set(verificationId, { sessionInfo, expiresAt: Date.now() + 10 * 60 * 1000 });

    return { verificationId };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error sending phone OTP:', error.response ? error.response.data : error.message);
    } else {
      console.error('Error sending phone OTP:', error);
    }
    throw new Error('Failed to send OTP to phone number');
  }
};

// Verify OTP
export const verifyPhoneOTP = async (verificationId: string, code: string): Promise<VerifyPhoneOTPResult> => {
  try {
    const otpData = otpStore.get(verificationId);
    if (!otpData) {
      throw new Error('Invalid or expired verification ID');
    }

    if (Date.now() > otpData.expiresAt) {
      otpStore.delete(verificationId);
      throw new Error('OTP has expired. Please request a new one.');
    }

    // Verify the OTP with Firebase
    const response = await axios.post<FirebaseVerifyOTPResponse>(
      `https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyPhoneNumber?key=${FIREBASE_API_KEY}`,
      {
        sessionInfo: otpData.sessionInfo,
        code,
      }
    );

    if (response.data.idToken) {
      otpStore.delete(verificationId);
      return { success: true, idToken: response.data.idToken };
    } else {
      throw new Error('Invalid OTP');
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Error verifying phone OTP:', error.response ? error.response.data : error.message);
    } else {
      console.error('Error verifying phone OTP:', error);
    }
    throw new Error('Invalid or expired OTP');
  }
};